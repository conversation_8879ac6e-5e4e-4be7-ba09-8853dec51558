# 今夜城堡项目2天紧急重构执行指南

## 🚨 紧急重构概览

**总时长**: 48小时连续作战  
**团队规模**: 4人不休息  
**目标**: 完成架构优化，消除代码重复，统一权限控制  

## ⏰ 详细时间表

### 第1天（0-24小时）

#### 0-6小时：基础架构搭建
```
00:00-02:00 | 架构师    | 创建BaseCrudController基类
02:00-04:00 | 开发者A   | 创建角色特化控制器基类
04:00-06:00 | 开发者B   | 设计权限验证钩子方法
```

#### 6-12小时：核心模块重构
```
06:00-08:00 | 开发者A   | 重构AdminDeviceController
06:00-08:00 | 开发者B   | 重构EntityDeviceController
08:00-10:00 | 开发者C   | 创建设备数据同步服务
10:00-12:00 | 测试工程师 | 设备模块功能测试
```

#### 12-18小时：财务模块重构
```
12:00-14:00 | 开发者A   | 重构AdminFinanceController
12:00-14:00 | 开发者B   | 重构EntityFinanceController
14:00-16:00 | 开发者C   | 优化BaseFinanceController
16:00-18:00 | 测试工程师 | 财务模块集成测试
```

#### 18-24小时：合作商和门店模块
```
18:00-20:00 | 开发者A   | 重构EntityPartnerController
18:00-20:00 | 开发者B   | 重构ShopController系列
20:00-22:00 | 开发者C   | 创建权限验证器
22:00-24:00 | 测试工程师 | 第1天集成测试
```

### 第2天（24-48小时）

#### 24-30小时：订单和用户模块
```
24:00-26:00 | 开发者A   | 重构OrderController系列
24:00-26:00 | 开发者B   | 重构AdminController
26:00-28:00 | 开发者C   | 创建服务层抽象接口
28:00-30:00 | 测试工程师 | 订单用户模块测试
```

#### 30-36小时：服务层重构
```
30:00-32:00 | 开发者A   | 实现BaseCrudServiceImpl
30:00-32:00 | 开发者B   | 重构DeviceService
32:00-34:00 | 开发者C   | 重构FinanceService
34:00-36:00 | 测试工程师 | 服务层集成测试
```

#### 36-42小时：权限统一化
```
36:00-38:00 | 开发者A   | 实现PermissionManager
36:00-38:00 | 开发者B   | 创建各模块权限验证器
38:00-40:00 | 开发者C   | 设备数据同步优化
40:00-42:00 | 测试工程师 | 权限功能测试
```

#### 42-48小时：全面测试和部署
```
42:00-44:00 | 全体     | 全系统功能测试
44:00-45:00 | 测试工程师 | 性能基准测试
45:00-46:00 | 开发者A   | 安全性测试
46:00-48:00 | 全体     | 生产环境部署准备
```

## 🛠️ 立即执行清单

### 准备阶段（开始前30分钟）

#### 环境准备
- [ ] 确认所有开发环境正常运行
- [ ] 检查网络连接稳定性
- [ ] 准备备用电脑和网络
- [ ] 设置代码自动备份（每小时）

#### 团队准备
- [ ] 确认4人团队到位
- [ ] 建立实时沟通群（微信/钉钉）
- [ ] 分配具体角色和职责
- [ ] 准备充足的食物和饮料

#### 代码准备
- [ ] 创建重构分支：`git checkout -b emergency-refactoring-48h`
- [ ] 备份当前代码：`git tag backup-before-refactoring`
- [ ] 确认项目可以正常编译和运行
- [ ] 准备测试数据和测试环境

### 第1小时立即执行任务

#### 架构师任务（00:00-02:00）
```java
// 立即创建：src/main/java/com/jycb/jycbz/common/controller/BaseCrudController.java
package com.jycb.jycbz.common.controller;

import com.jycb.jycbz.common.annotation.DataPermission;
import com.jycb.jycbz.common.api.CommonResult;
import com.jycb.jycbz.common.api.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import java.util.List;

@Slf4j
public abstract class BaseCrudController<T, VO, DTO> {

    @GetMapping("/page")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<PageResult<VO>> page(@Valid @RequestBody DTO queryDTO) {
        validateListPermission(queryDTO);
        PageResult<VO> result = executePageQuery(queryDTO);
        postProcessPageResult(result);
        return CommonResult.success(result);
    }

    @GetMapping("/{id}")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<VO> getById(@PathVariable Long id) {
        VO result = executeGetById(id);
        validateEntityPermission(result);
        return CommonResult.success(result);
    }

    @PostMapping
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<VO> create(@Valid @RequestBody DTO dto) {
        validateCreatePermission(dto);
        preprocessCreate(dto);
        VO result = executeCreate(dto);
        postProcessCreate(result);
        return CommonResult.success(result);
    }

    @PutMapping("/{id}")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<VO> update(@PathVariable Long id, @Valid @RequestBody DTO dto) {
        validateUpdatePermission(id, dto);
        preprocessUpdate(id, dto);
        VO result = executeUpdate(id, dto);
        postProcessUpdate(result);
        return CommonResult.success(result);
    }

    @DeleteMapping("/{id}")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Void> deleteById(@PathVariable Long id) {
        validateDeletePermission(id);
        preprocessDelete(id);
        executeDelete(id);
        postProcessDelete(id);
        return CommonResult.success();
    }

    // 抽象方法 - 子类必须实现
    protected abstract PageResult<VO> executePageQuery(DTO queryDTO);
    protected abstract VO executeGetById(Long id);
    protected abstract VO executeCreate(DTO dto);
    protected abstract VO executeUpdate(Long id, DTO dto);
    protected abstract void executeDelete(Long id);

    // 钩子方法 - 子类可重写
    protected void validateListPermission(DTO queryDTO) {}
    protected void validateEntityPermission(VO entity) {}
    protected void validateCreatePermission(DTO dto) {}
    protected void validateUpdatePermission(Long id, DTO dto) {}
    protected void validateDeletePermission(Long id) {}
    protected void preprocessCreate(DTO dto) {}
    protected void preprocessUpdate(Long id, DTO dto) {}
    protected void preprocessDelete(Long id) {}
    protected void postProcessPageResult(PageResult<VO> result) {}
    protected void postProcessCreate(VO result) {}
    protected void postProcessUpdate(VO result) {}
    protected void postProcessDelete(Long id) {}
}
```

#### 检查点（2小时后）
- [ ] BaseCrudController创建完成
- [ ] 代码编译通过
- [ ] 提交代码：`git add . && git commit -m "创建BaseCrudController基类"`

## 🔄 每6小时检查点

### 6小时检查点
- [ ] 基础架构搭建完成
- [ ] 通用控制器可用
- [ ] 代码编译通过
- [ ] 创建备份分支

### 12小时检查点
- [ ] 设备模块重构完成
- [ ] 基本功能测试通过
- [ ] 权限控制正常
- [ ] 创建备份分支

### 18小时检查点
- [ ] 财务模块重构完成
- [ ] 集成测试通过
- [ ] 数据权限隔离有效
- [ ] 创建备份分支

### 24小时检查点（第1天完成）
- [ ] 核心模块重构完成
- [ ] 主要业务功能正常
- [ ] 权限验证有效
- [ ] 创建稳定分支

### 36小时检查点
- [ ] 服务层重构完成
- [ ] 统一架构可用
- [ ] 集成测试通过
- [ ] 创建备份分支

### 48小时检查点（完成）
- [ ] 全部重构完成
- [ ] 所有测试通过
- [ ] 可以部署上线
- [ ] 创建发布分支

## ⚠️ 紧急应对措施

### 如果进度落后
1. **优先级调整**: 专注核心模块，暂缓次要功能
2. **并行开发**: 增加并行任务，减少依赖等待
3. **简化实现**: 采用最简实现，后续优化

### 如果出现Bug
1. **立即回滚**: 回到最近的稳定备份点
2. **快速修复**: 2人专门负责Bug修复
3. **继续开发**: 其他人继续后续任务

### 如果人员疲劳
1. **轮换休息**: 每人轮流休息15-30分钟
2. **任务调整**: 疲劳时做简单任务
3. **相互支援**: 团队成员相互帮助

## 🎯 成功标准

### 第1天结束标准
- 设备、财务、合作商、门店模块重构完成
- 基本CRUD功能正常
- 权限控制有效
- 代码编译通过

### 第2天结束标准
- 所有模块重构完成
- 服务层统一架构
- 权限控制统一化
- 全面测试通过
- 可以部署上线

立即开始执行！时间就是生命！
