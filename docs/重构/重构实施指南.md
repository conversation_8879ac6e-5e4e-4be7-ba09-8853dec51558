# 今夜城堡项目重构实施指南

## 1. 重构前准备工作

### 1.1 代码备份和分支管理

```bash
# 创建重构主分支
git checkout -b feature/architecture-refactoring

# 创建各阶段分支
git checkout -b phase1/base-controller-refactoring
git checkout -b phase2/permission-unification
git checkout -b phase3/service-layer-abstraction
git checkout -b phase4/device-data-sync
```

### 1.2 环境准备

1. **测试环境配置**
   - 准备完整的测试数据
   - 配置自动化测试流水线
   - 设置性能监控基线

2. **开发工具准备**
   - IDE插件更新
   - 代码质量检查工具
   - 重构辅助工具

### 1.3 团队准备

1. **技术培训**
   - 新架构设计理念
   - 重构最佳实践
   - 测试驱动开发

2. **分工安排**
   - 架构师：整体设计和关键决策
   - 高级开发：核心模块重构
   - 开发工程师：辅助开发和测试

## 2. 阶段一：基础控制器重构

### 2.1 创建通用基础控制器

**目标文件**: `src/main/java/com/jycb/jycbz/common/controller/BaseCrudController.java`

```java
package com.jycb.jycbz.common.controller;

import com.jycb.jycbz.common.annotation.DataPermission;
import com.jycb.jycbz.common.api.CommonResult;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.dto.PageQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 通用CRUD基础控制器
 */
@Slf4j
public abstract class BaseCrudController<T, VO, DTO> {

    /**
     * 分页查询
     */
    @GetMapping("/page")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<PageResult<VO>> page(@Valid PageQueryDTO pageQuery) {
        validateListPermission(pageQuery);
        PageResult<VO> result = executePageQuery(pageQuery);
        postProcessPageResult(result);
        return CommonResult.success(result);
    }

    /**
     * 根据ID查询详情
     */
    @GetMapping("/{id}")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<VO> getById(@PathVariable Long id) {
        VO result = executeGetById(id);
        validateEntityPermission(result);
        return CommonResult.success(result);
    }

    /**
     * 创建
     */
    @PostMapping
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<VO> create(@Valid @RequestBody DTO dto) {
        validateCreatePermission(dto);
        preprocessCreate(dto);
        VO result = executeCreate(dto);
        postProcessCreate(result);
        return CommonResult.success(result);
    }

    /**
     * 更新
     */
    @PutMapping("/{id}")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<VO> update(@PathVariable Long id, @Valid @RequestBody DTO dto) {
        validateUpdatePermission(id, dto);
        preprocessUpdate(id, dto);
        VO result = executeUpdate(id, dto);
        postProcessUpdate(result);
        return CommonResult.success(result);
    }

    /**
     * 删除
     */
    @DeleteMapping("/{id}")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Void> deleteById(@PathVariable Long id) {
        validateDeletePermission(id);
        preprocessDelete(id);
        executeDelete(id);
        postProcessDelete(id);
        return CommonResult.success();
    }

    /**
     * 批量删除
     */
    @DeleteMapping("/batch")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Void> deleteBatch(@RequestBody List<Long> ids) {
        validateBatchDeletePermission(ids);
        executeBatchDelete(ids);
        return CommonResult.success();
    }

    // ==================== 抽象方法，子类必须实现 ====================
    
    protected abstract PageResult<VO> executePageQuery(PageQueryDTO pageQuery);
    protected abstract VO executeGetById(Long id);
    protected abstract VO executeCreate(DTO dto);
    protected abstract VO executeUpdate(Long id, DTO dto);
    protected abstract void executeDelete(Long id);
    protected abstract void executeBatchDelete(List<Long> ids);

    // ==================== 钩子方法，子类可以重写 ====================
    
    protected void validateListPermission(PageQueryDTO pageQuery) {}
    protected void validateEntityPermission(VO entity) {}
    protected void validateCreatePermission(DTO dto) {}
    protected void validateUpdatePermission(Long id, DTO dto) {}
    protected void validateDeletePermission(Long id) {}
    protected void validateBatchDeletePermission(List<Long> ids) {
        ids.forEach(this::validateDeletePermission);
    }
    
    protected void preprocessCreate(DTO dto) {}
    protected void preprocessUpdate(Long id, DTO dto) {}
    protected void preprocessDelete(Long id) {}
    
    protected void postProcessPageResult(PageResult<VO> result) {}
    protected void postProcessCreate(VO result) {}
    protected void postProcessUpdate(VO result) {}
    protected void postProcessDelete(Long id) {}
}
```

### 2.2 创建角色特化控制器

**系统管理员控制器基类**:

```java
package com.jycb.jycbz.common.controller;

import com.jycb.jycbz.common.dto.PageQueryDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * 系统管理员控制器基类
 */
@Slf4j
public abstract class BaseSystemAdminController<T, VO, DTO> 
    extends BaseCrudController<T, VO, DTO> {

    @Override
    protected void validateListPermission(PageQueryDTO pageQuery) {
        // 系统管理员拥有全局权限，无需额外验证
        log.debug("系统管理员访问，跳过权限验证");
    }

    @Override
    protected void validateEntityPermission(VO entity) {
        // 系统管理员拥有全局权限，无需额外验证
        log.debug("系统管理员访问，跳过权限验证");
    }
}
```

**业务主体管理员控制器基类**:

```java
package com.jycb.jycbz.common.controller;

import com.jycb.jycbz.common.dto.PageQueryDTO;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 业务主体管理员控制器基类
 */
@Slf4j
public abstract class BaseEntityAdminController<T, VO, DTO> 
    extends BaseCrudController<T, VO, DTO> {

    /**
     * 获取当前登录用户的业务主体ID
     */
    protected Long getCurrentEntityId() {
        return SecurityUtils.getCurrentEntityId();
    }

    @Override
    protected void validateCreatePermission(DTO dto) {
        super.validateCreatePermission(dto);
        validateEntityScope(dto);
    }

    @Override
    protected void validateUpdatePermission(Long id, DTO dto) {
        super.validateUpdatePermission(id, dto);
        validateEntityScope(dto);
    }

    /**
     * 验证业务主体范围，子类实现具体逻辑
     */
    protected abstract void validateEntityScope(DTO dto);
}
```

### 2.3 重构现有控制器示例

**设备控制器重构**:

```java
package com.jycb.jycbz.modules.device.controller.admin;

import com.jycb.jycbz.common.controller.BaseSystemAdminController;
import com.jycb.jycbz.common.api.CommonResult;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.dto.PageQueryDTO;
import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.vo.DeviceVO;
import com.jycb.jycbz.modules.device.dto.DeviceDTO;
import com.jycb.jycbz.modules.device.dto.DeviceDataDTO;
import com.jycb.jycbz.modules.device.service.DeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统管理员设备控制器 - 重构版
 */
@RestController
@RequestMapping("/admin/device")
@Tag(name = "系统管理员-设备管理")
public class AdminDeviceController extends BaseSystemAdminController<Device, DeviceVO, DeviceDTO> {
    
    @Autowired
    private DeviceService deviceService;
    
    // ==================== 实现抽象方法 ====================
    
    @Override
    protected PageResult<DeviceVO> executePageQuery(PageQueryDTO pageQuery) {
        return deviceService.pageQuery(pageQuery);
    }
    
    @Override
    protected DeviceVO executeGetById(Long id) {
        return deviceService.getDeviceVO(id);
    }
    
    @Override
    protected DeviceVO executeCreate(DeviceDTO dto) {
        return deviceService.createDevice(dto);
    }
    
    @Override
    protected DeviceVO executeUpdate(Long id, DeviceDTO dto) {
        return deviceService.updateDevice(id, dto);
    }
    
    @Override
    protected void executeDelete(Long id) {
        deviceService.deleteDevice(id);
    }
    
    @Override
    protected void executeBatchDelete(List<Long> ids) {
        deviceService.batchDeleteDevices(ids);
    }
    
    // ==================== 设备特有操作 ====================
    
    @PostMapping("/{id}/sync-data")
    @Operation(summary = "同步设备数据")
    public CommonResult<Void> syncDeviceData(@PathVariable Long id, @RequestBody DeviceDataDTO dataDTO) {
        deviceService.syncDeviceData(id, dataDTO);
        return CommonResult.success();
    }
    
    @PostMapping("/{id}/bind")
    @Operation(summary = "绑定设备")
    public CommonResult<Void> bindDevice(@PathVariable Long id, @RequestBody DeviceBindDTO bindDTO) {
        deviceService.bindDevice(id, bindDTO);
        return CommonResult.success();
    }
    
    @PostMapping("/{id}/unbind")
    @Operation(summary = "解绑设备")
    public CommonResult<Void> unbindDevice(@PathVariable Long id) {
        deviceService.unbindDevice(id);
        return CommonResult.success();
    }
}
```

## 3. 阶段二：权限控制统一化

### 3.1 创建权限验证器接口

```java
package com.jycb.jycbz.common.permission;

import com.jycb.jycbz.common.context.DataPermissionContext;

/**
 * 权限验证器接口
 */
public interface PermissionValidator<T> {
    
    /**
     * 检查是否可以访问
     */
    boolean canAccess(T target, DataPermissionContext.DataPermissionInfo permission);
    
    /**
     * 验证访问权限，失败时抛出异常
     */
    void validateAccess(T target, DataPermissionContext.DataPermissionInfo permission);
    
    /**
     * 获取支持的实体类型
     */
    Class<T> getSupportedType();
}
```

### 3.2 实现权限管理器

```java
package com.jycb.jycbz.common.permission;

import com.jycb.jycbz.common.context.DataPermissionContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 统一权限管理器
 */
@Component
@RequiredArgsConstructor
public class PermissionManager {
    
    private final Map<Class<?>, PermissionValidator<?>> validators;
    
    @SuppressWarnings("unchecked")
    public <T> void validateAccess(T target) {
        if (target == null) {
            return;
        }
        
        Class<?> targetClass = target.getClass();
        PermissionValidator<T> validator = (PermissionValidator<T>) validators.get(targetClass);
        
        if (validator != null) {
            DataPermissionContext.DataPermissionInfo permission = DataPermissionContext.get();
            validator.validateAccess(target, permission);
        }
    }
    
    public <T> boolean canAccess(T target) {
        try {
            validateAccess(target);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
```

## 4. 阶段三：设备数据同步优化

### 4.1 设备数据同步服务

```java
package com.jycb.jycbz.modules.device.service;

import com.jycb.jycbz.modules.device.dto.DeviceDataDTO;
import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.entity.DeviceUsageLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 设备数据同步服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeviceDataSyncService {
    
    private final DeviceService deviceService;
    private final DeviceUsageLogService deviceUsageLogService;
    
    /**
     * 处理用户上传的设备数据
     */
    @Transactional
    public void handleDeviceDataUpload(Long deviceId, DeviceDataDTO deviceData) {
        log.info("处理设备数据上传，设备ID: {}", deviceId);
        
        try {
            // 1. 验证设备数据
            validateDeviceData(deviceData);
            
            // 2. 更新设备最后使用时间
            updateDeviceLastUsedTime(deviceId);
            
            // 3. 记录设备使用日志
            recordDeviceUsage(deviceId, deviceData);
            
            // 4. 处理业务逻辑
            processBusinessLogic(deviceId, deviceData);
            
            log.info("设备数据处理完成，设备ID: {}", deviceId);
            
        } catch (Exception e) {
            log.error("设备数据处理失败，设备ID: {}", deviceId, e);
            throw e;
        }
    }
    
    private void validateDeviceData(DeviceDataDTO deviceData) {
        if (deviceData == null) {
            throw new BusinessException("设备数据不能为空");
        }
        
        if (deviceData.getTimestamp() == null) {
            throw new BusinessException("设备数据时间戳不能为空");
        }
        
        // 其他验证逻辑
    }
    
    private void updateDeviceLastUsedTime(Long deviceId) {
        Device device = deviceService.getById(deviceId);
        if (device != null) {
            device.setLastUsedTime(LocalDateTime.now());
            deviceService.updateById(device);
        }
    }
    
    private void recordDeviceUsage(Long deviceId, DeviceDataDTO deviceData) {
        DeviceUsageLog log = new DeviceUsageLog();
        log.setDeviceId(deviceId);
        log.setUsageTime(LocalDateTime.now());
        log.setUsageData(deviceData.toString());
        log.setUserId(deviceData.getUserId());
        deviceUsageLogService.save(log);
    }
    
    private void processBusinessLogic(Long deviceId, DeviceDataDTO deviceData) {
        // 根据设备数据处理具体业务逻辑
        // 如创建订单、更新库存等
        
        // 示例：如果是扫码数据，可能需要创建订单
        if ("SCAN".equals(deviceData.getDataType())) {
            // 处理扫码业务逻辑
            processScanData(deviceId, deviceData);
        }
    }
    
    private void processScanData(Long deviceId, DeviceDataDTO deviceData) {
        // 处理扫码相关的业务逻辑
        log.info("处理扫码数据，设备ID: {}, 扫码内容: {}", deviceId, deviceData.getContent());
    }
}
```

## 5. 测试和验证

### 5.1 单元测试

```java
@ExtendWith(MockitoExtension.class)
class BaseCrudControllerTest {
    
    @Mock
    private DeviceService deviceService;
    
    @InjectMocks
    private AdminDeviceController controller;
    
    @Test
    void testPageQuery() {
        // 测试分页查询功能
        PageQueryDTO queryDTO = new PageQueryDTO();
        PageResult<DeviceVO> expectedResult = new PageResult<>();
        
        when(deviceService.pageQuery(queryDTO)).thenReturn(expectedResult);
        
        CommonResult<PageResult<DeviceVO>> result = controller.page(queryDTO);
        
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo(expectedResult);
    }
}
```

### 5.2 集成测试

```java
@SpringBootTest
@AutoConfigureTestDatabase
class DeviceControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testDeviceCrudWorkflow() {
        // 测试设备的完整CRUD流程
        
        // 1. 创建设备
        DeviceDTO createDTO = new DeviceDTO();
        // 设置创建参数...
        
        ResponseEntity<CommonResult> createResponse = restTemplate.postForEntity(
            "/admin/device", createDTO, CommonResult.class);
        
        assertThat(createResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 2. 查询设备
        // 3. 更新设备
        // 4. 删除设备
    }
}
```

这个实施指南提供了详细的重构步骤和代码示例，确保重构过程的顺利进行。
