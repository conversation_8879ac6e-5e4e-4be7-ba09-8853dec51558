# 今夜城堡系统 API 文档

## 概述

本文档详细描述了今夜城堡系统的API接口，主要面向两个核心角色：
- **系统管理员**：拥有系统最高权限，可管理所有模块
- **业务主体管理员**：管理特定业务主体下的资源和下级

## 文档结构

### 系统管理员 API
- [管理员管理](./system-admin/admin-management.md) - 管理员账户的增删改查 ✅
- [角色权限管理](./system-admin/role-permission.md) - 角色和权限的管理 ✅
- [设备管理](./system-admin/device-management.md) - 设备的全生命周期管理 ✅
- [业务主体管理](./system-admin/entity-management.md) - 业务主体的管理 🚧
- [系统配置](./system-admin/system-config.md) - 系统级配置管理 🚧
- [监控运维](./system-admin/monitoring.md) - 系统监控和运维功能 🚧

### 业务主体管理员 API
- [合作商管理](./entity-admin/partner-management.md) - 下级合作商管理 ✅
- [门店管理](./entity-admin/shop-management.md) - 门店管理 🚧
- [设备管理](./entity-admin/device-management.md) - 业务主体设备管理 ✅
- [财务管理](./entity-admin/finance-management.md) - 财务数据管理 ✅
- [统计报表](./entity-admin/statistics.md) - 业务统计和报表 🚧

## 通用说明

### 认证方式
系统使用 Sa-Token 进行身份认证和权限控制。

### 请求格式
- Content-Type: application/json
- 字符编码: UTF-8

### 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T00:00:00"
}
```

### 状态码说明
- 200: 成功
- 400: 请求参数错误
- 401: 未认证
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

### 权限控制
- `@SaCheckLogin`: 需要登录
- `@SaCheckRole`: 需要特定角色
- `@SaCheckPermission`: 需要特定权限
- `@DataPermission`: 数据权限控制

### 角色说明
- `SUPER_ADMIN`: 超级管理员
- `SYSTEM_ADMIN`: 系统管理员
- `ENTITY_ADMIN`: 业务主体管理员
- `PARTNER_ADMIN`: 合作商管理员
- `SHOP_ADMIN`: 门店管理员

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完成核心API文档编写
