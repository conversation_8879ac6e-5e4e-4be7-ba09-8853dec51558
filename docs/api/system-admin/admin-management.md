# 管理员管理 API

## 概述
管理员管理模块提供对系统管理员账户的完整生命周期管理，包括创建、查询、更新、删除等操作。

**基础路径**: `/admin/admin`

**权限要求**: 系统管理员角色 (`SYSTEM_ADMIN`)

## API 列表

### 1. 分页查询管理员列表

**接口**: `GET /admin/admin/page`

**描述**: 分页查询管理员列表，支持多种筛选条件

**权限**: `admin:admin:list`

**请求参数**:
```json
{
  "current": 1,           // 页码，默认1
  "size": 10,            // 每页数量，默认10
  "username": "admin",    // 用户名（可选）
  "realName": "张三",     // 真实姓名（可选）
  "status": 1,           // 状态：1-启用 0-禁用（可选）
  "adminType": "SYSTEM", // 管理员类型（可选）
  "entityId": 1,         // 业务主体ID（可选）
  "partnerId": 1,        // 合作商ID（可选）
  "createTimeStart": "2024-01-01", // 创建时间开始（可选）
  "createTimeEnd": "2024-12-31"    // 创建时间结束（可选）
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "username": "admin",
        "realName": "系统管理员",
        "mobile": "13800138000",
        "email": "<EMAIL>",
        "adminType": "SYSTEM",
        "status": 1,
        "entityId": null,
        "partnerId": null,
        "shopId": null,
        "lastLoginTime": "2024-01-01T10:00:00",
        "createTime": "2024-01-01T00:00:00",
        "updateTime": "2024-01-01T10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 获取管理员详情

**接口**: `GET /admin/admin/{id}`

**描述**: 根据ID获取管理员详细信息

**权限**: `admin:admin:detail`

**路径参数**:
- `id` (Long): 管理员ID

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "username": "admin",
    "realName": "系统管理员",
    "mobile": "13800138000",
    "email": "<EMAIL>",
    "avatar": "http://example.com/avatar.jpg",
    "adminType": "SYSTEM",
    "status": 1,
    "entityId": null,
    "partnerId": null,
    "shopId": null,
    "lastLoginTime": "2024-01-01T10:00:00",
    "createTime": "2024-01-01T00:00:00",
    "updateTime": "2024-01-01T10:00:00",
    "roles": [
      {
        "id": 1,
        "roleName": "系统管理员",
        "roleCode": "SYSTEM_ADMIN"
      }
    ]
  }
}
```

### 3. 创建管理员

**接口**: `POST /admin/admin`

**描述**: 创建新的管理员账户

**权限**: `admin:admin:create`

**请求体**:
```json
{
  "username": "newadmin",        // 用户名（必填，唯一）
  "password": "123456",          // 密码（必填）
  "realName": "新管理员",        // 真实姓名（必填）
  "mobile": "13800138001",       // 手机号（可选）
  "email": "<EMAIL>",    // 邮箱（可选）
  "adminType": "ENTITY",         // 管理员类型（必填）
  "entityId": 1,                 // 业务主体ID（可选）
  "partnerId": 1,                // 合作商ID（可选）
  "shopId": 1,                   // 门店ID（可选）
  "status": 1,                   // 状态：1-启用 0-禁用
  "roleIds": [2, 3]              // 角色ID列表（可选）
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "管理员创建成功",
  "data": "管理员创建成功"
}
```

### 4. 更新管理员信息

**接口**: `PUT /admin/admin`

**描述**: 更新管理员基本信息

**权限**: `admin:admin:update`

**请求体**:
```json
{
  "id": 1,                       // 管理员ID（必填）
  "realName": "更新后的姓名",     // 真实姓名（可选）
  "mobile": "13800138002",       // 手机号（可选）
  "email": "<EMAIL>", // 邮箱（可选）
  "avatar": "http://example.com/new-avatar.jpg", // 头像（可选）
  "status": 1                    // 状态（可选）
}
```

### 5. 修改密码

**接口**: `PUT /admin/admin/password`

**描述**: 修改管理员密码

**权限**: `admin:admin:password`

**请求体**:
```json
{
  "adminId": 1,          // 管理员ID（必填）
  "oldPassword": "123456", // 原密码（必填）
  "newPassword": "654321"  // 新密码（必填）
}
```

### 6. 删除管理员

**接口**: `DELETE /admin/admin/{id}`

**描述**: 根据ID删除管理员

**权限**: `SYSTEM_ADMIN` 角色

**路径参数**:
- `id` (Long): 管理员ID

### 7. 更新管理员状态

**接口**: `PUT /admin/admin/{id}/status`

**描述**: 启用或禁用管理员账户

**权限**: `SYSTEM_ADMIN` 角色

**路径参数**:
- `id` (Long): 管理员ID

**请求参数**:
- `status` (Integer): 状态，1-启用 0-禁用

### 8. 重置密码

**接口**: `PUT /admin/admin/{id}/password`

**描述**: 重置管理员密码

**权限**: `SYSTEM_ADMIN` 角色

**路径参数**:
- `id` (Long): 管理员ID

**请求参数**:
- `newPassword` (String): 新密码

### 9. 角色管理

#### 9.1 分配角色

**接口**: `POST /admin/admin/{id}/roles/{roleId}`

**描述**: 为管理员分配角色

**权限**: `admin:role:assign`

#### 9.2 移除角色

**接口**: `DELETE /admin/admin/{id}/roles/{roleId}`

**描述**: 移除管理员的角色

**权限**: `admin:role:remove`

#### 9.3 获取管理员角色列表

**接口**: `GET /admin/admin/{id}/roles`

**描述**: 获取指定管理员的角色列表

**权限**: `admin:role:list`

#### 9.4 获取管理员权限列表

**接口**: `GET /admin/admin/{id}/permissions`

**描述**: 获取指定管理员的权限列表

**权限**: `admin:permission:list`

### 10. 批量操作

#### 10.1 批量删除管理员

**接口**: `DELETE /admin/admin/batch`

**描述**: 批量删除管理员

**权限**: `admin:delete`

**请求体**:
```json
[1, 2, 3]  // 管理员ID数组
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400001 | 用户名已存在 |
| 400002 | 手机号已存在 |
| 400003 | 邮箱已存在 |
| 400004 | 管理员不存在 |
| 400005 | 原密码错误 |
| 403001 | 权限不足 |
| 403002 | 不能删除自己的账户 |

## 注意事项

1. 管理员用户名必须唯一
2. 手机号和邮箱如果填写必须唯一
3. 不能删除自己的账户
4. 密码长度至少6位
5. 管理员类型一旦创建不可修改
6. 数据权限控制确保只能管理有权限的管理员
