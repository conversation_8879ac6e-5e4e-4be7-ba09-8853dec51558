# 角色权限管理 API

## 概述
角色权限管理模块提供对系统角色和权限的完整管理功能，包括角色的增删改查、权限的分配和管理等。

## 角色管理 API

**基础路径**: `/api/system/role`

**权限要求**: 系统管理员角色

### 1. 分页查询角色列表

**接口**: `GET /api/system/role/page`

**描述**: 分页查询角色列表

**权限**: `role:list`

**请求参数**:
```json
{
  "current": 1,           // 页码，默认1
  "size": 10,            // 每页数量，默认10
  "roleName": "管理员",   // 角色名称（可选）
  "roleCode": "ADMIN",   // 角色编码（可选）
  "status": 1            // 状态：1-启用 0-禁用（可选）
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "roleName": "系统管理员",
        "roleCode": "SYSTEM_ADMIN",
        "description": "系统管理员角色",
        "status": 1,
        "createTime": "2024-01-01T00:00:00",
        "updateTime": "2024-01-01T10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 获取角色详情

**接口**: `GET /api/system/role/{id}`

**描述**: 根据ID获取角色详细信息

**权限**: `role:detail`

**路径参数**:
- `id` (Long): 角色ID

### 3. 创建角色

**接口**: `POST /api/system/role`

**描述**: 创建新角色

**权限**: `role:create`

**请求体**:
```json
{
  "roleName": "新角色",           // 角色名称（必填）
  "roleCode": "NEW_ROLE",        // 角色编码（必填，唯一）
  "description": "角色描述",      // 角色描述（可选）
  "status": 1,                   // 状态：1-启用 0-禁用
  "permissionIds": [1, 2, 3]     // 权限ID列表（可选）
}
```

### 4. 更新角色

**接口**: `PUT /api/system/role/{id}`

**描述**: 更新角色信息

**权限**: `role:update`

### 5. 删除角色

**接口**: `DELETE /api/system/role/{id}`

**描述**: 删除角色

**权限**: `role:delete`

### 6. 批量删除角色

**接口**: `DELETE /api/system/role/batch`

**描述**: 批量删除角色

**权限**: `role:delete`

### 7. 更新角色状态

**接口**: `PUT /api/system/role/{id}/status`

**描述**: 启用或禁用角色

**权限**: `role:status`

### 8. 获取所有角色

**接口**: `GET /api/system/role/all`

**描述**: 获取所有可用角色

**权限**: `role:list`

### 9. 角色权限管理

#### 9.1 获取角色权限列表

**接口**: `GET /api/system/role/{id}/permissions`

**描述**: 获取指定角色的权限列表

**权限**: `role:permission:list`

#### 9.2 分配权限给角色

**接口**: `POST /api/system/role/{id}/permissions`

**描述**: 为角色分配权限

**权限**: `role:permission:assign`

**请求体**:
```json
[1, 2, 3, 4]  // 权限ID数组
```

#### 9.3 移除角色权限

**接口**: `DELETE /api/system/role/{id}/permissions/{permissionId}`

**描述**: 移除角色的指定权限

**权限**: `role:permission:remove`

## 权限管理 API

**基础路径**: `/api/system/permission`

### 1. 分页查询权限列表

**接口**: `GET /api/system/permission/page`

**描述**: 分页查询权限列表

**权限**: `permission:list`

**请求参数**:
```json
{
  "current": 1,              // 页码，默认1
  "size": 10,               // 每页数量，默认10
  "permissionName": "用户",  // 权限名称（可选）
  "permissionCode": "user",  // 权限编码（可选）
  "module": "USER",         // 模块（可选）
  "status": 1               // 状态：1-启用 0-禁用（可选）
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "permissionName": "用户管理",
        "permissionCode": "user:list",
        "module": "USER",
        "description": "用户列表查看权限",
        "status": 1,
        "createTime": "2024-01-01T00:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 获取权限详情

**接口**: `GET /api/system/permission/{id}`

**描述**: 根据ID获取权限详细信息

**权限**: `permission:detail`

### 3. 创建权限

**接口**: `POST /api/system/permission`

**描述**: 创建新权限

**权限**: `permission:create`

**请求体**:
```json
{
  "permissionName": "新权限",        // 权限名称（必填）
  "permissionCode": "new:permission", // 权限编码（必填，唯一）
  "module": "SYSTEM",               // 模块（必填）
  "description": "权限描述",         // 权限描述（可选）
  "status": 1                       // 状态：1-启用 0-禁用
}
```

### 4. 更新权限

**接口**: `PUT /api/system/permission/{id}`

**描述**: 更新权限信息

**权限**: `permission:update`

### 5. 删除权限

**接口**: `DELETE /api/system/permission/{id}`

**描述**: 删除权限

**权限**: `permission:delete`

### 6. 批量删除权限

**接口**: `DELETE /api/system/permission/batch`

**描述**: 批量删除权限

**权限**: `permission:delete`

### 7. 更新权限状态

**接口**: `PUT /api/system/permission/{id}/status`

**描述**: 启用或禁用权限

**权限**: `permission:status`

### 8. 获取所有权限

**接口**: `GET /api/system/permission/all`

**描述**: 获取系统中所有权限

**权限**: `permission:list`

### 9. 获取当前用户权限

**接口**: `GET /api/system/permission/current`

**描述**: 获取当前登录用户的权限列表

**权限**: 需要登录

### 10. 根据角色获取权限

**接口**: `GET /api/system/permission/role/{roleId}`

**描述**: 根据角色ID获取权限列表

**权限**: `permission:list`

### 11. 获取可用权限

**接口**: `GET /api/system/permission/available`

**描述**: 获取所有状态为启用的权限

**权限**: `permission:list`

### 12. 检查权限标识

**接口**: `GET /api/system/permission/check`

**描述**: 检查权限标识是否已存在

**权限**: `permission:check`

**请求参数**:
- `permission` (String): 权限标识
- `excludeId` (Long): 排除的权限ID（可选）

## 权限模块说明

系统权限按模块划分，主要包括：

| 模块 | 说明 |
|------|------|
| ADMIN | 管理员管理 |
| ROLE | 角色管理 |
| PERMISSION | 权限管理 |
| ENTITY | 业务主体管理 |
| PARTNER | 合作商管理 |
| SHOP | 门店管理 |
| DEVICE | 设备管理 |
| FINANCE | 财务管理 |
| ORDER | 订单管理 |
| SYSTEM | 系统配置 |

## 权限命名规范

权限编码采用 `模块:操作` 的格式，例如：
- `user:list` - 用户列表查看
- `user:create` - 用户创建
- `user:update` - 用户更新
- `user:delete` - 用户删除
- `user:detail` - 用户详情查看

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400101 | 角色名称已存在 |
| 400102 | 角色编码已存在 |
| 400103 | 角色不存在 |
| 400104 | 角色正在使用中，无法删除 |
| 400201 | 权限名称已存在 |
| 400202 | 权限编码已存在 |
| 400203 | 权限不存在 |
| 400204 | 权限正在使用中，无法删除 |

## 注意事项

1. 角色编码和权限编码必须唯一
2. 删除角色前需确保没有管理员使用该角色
3. 删除权限前需确保没有角色使用该权限
4. 系统内置角色和权限不可删除
5. 权限分配立即生效，无需重新登录
