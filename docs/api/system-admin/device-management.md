# 设备管理 API

## 概述
设备管理模块提供对系统中所有设备的全生命周期管理，包括设备的注册、配置、监控、维护等功能。

**基础路径**: `/admin/device`

**权限要求**: 系统管理员角色

## 设备基础管理

### 1. 分页查询设备列表

**接口**: `GET /admin/device/page`

**描述**: 分页查询设备列表，支持多种筛选条件

**权限**: `device:list`

**请求参数**:
```json
{
  "current": 1,              // 页码，默认1
  "size": 10,               // 每页数量，默认10
  "deviceCode": "DEV001",   // 设备编码（可选）
  "deviceName": "设备1",     // 设备名称（可选）
  "deviceType": "SCANNER",  // 设备类型（可选）
  "status": 1,              // 设备状态（可选）
  "entityId": 1,            // 业务主体ID（可选）
  "partnerId": 1,           // 合作商ID（可选）
  "shopId": 1,              // 门店ID（可选）
  "onlineStatus": 1,        // 在线状态：1-在线 0-离线（可选）
  "createTimeStart": "2024-01-01", // 创建时间开始（可选）
  "createTimeEnd": "2024-12-31"    // 创建时间结束（可选）
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "deviceCode": "DEV001",
        "deviceName": "扫码设备1",
        "deviceType": "SCANNER",
        "model": "SC-2024",
        "manufacturer": "厂商A",
        "status": 1,
        "onlineStatus": 1,
        "entityId": 1,
        "entityName": "业务主体A",
        "partnerId": 1,
        "partnerName": "合作商A",
        "shopId": 1,
        "shopName": "门店A",
        "installLocation": "门店入口",
        "lastHeartbeatTime": "2024-01-01T10:00:00",
        "createTime": "2024-01-01T00:00:00",
        "updateTime": "2024-01-01T10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 获取设备详情

**接口**: `GET /admin/device/{id}`

**描述**: 根据ID获取设备详细信息

**权限**: `device:detail`

**路径参数**:
- `id` (Long): 设备ID

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "deviceCode": "DEV001",
    "deviceName": "扫码设备1",
    "deviceType": "SCANNER",
    "model": "SC-2024",
    "manufacturer": "厂商A",
    "serialNumber": "SN123456789",
    "macAddress": "AA:BB:CC:DD:EE:FF",
    "ipAddress": "*************",
    "status": 1,
    "onlineStatus": 1,
    "entityId": 1,
    "partnerId": 1,
    "shopId": 1,
    "installLocation": "门店入口",
    "installTime": "2024-01-01T00:00:00",
    "lastHeartbeatTime": "2024-01-01T10:00:00",
    "firmwareVersion": "v1.0.0",
    "hardwareVersion": "v1.0",
    "configVersion": "v1.0",
    "description": "设备描述",
    "createTime": "2024-01-01T00:00:00",
    "updateTime": "2024-01-01T10:00:00"
  }
}
```

### 3. 创建设备

**接口**: `POST /admin/device`

**描述**: 注册新设备

**权限**: `device:create`

**请求体**:
```json
{
  "deviceCode": "DEV002",        // 设备编码（必填，唯一）
  "deviceName": "新设备",        // 设备名称（必填）
  "deviceType": "SCANNER",       // 设备类型（必填）
  "model": "SC-2024",           // 设备型号（可选）
  "manufacturer": "厂商A",       // 制造商（可选）
  "serialNumber": "SN987654321", // 序列号（可选）
  "macAddress": "FF:EE:DD:CC:BB:AA", // MAC地址（可选）
  "entityId": 1,                // 业务主体ID（必填）
  "partnerId": 1,               // 合作商ID（可选）
  "shopId": 1,                  // 门店ID（可选）
  "installLocation": "门店收银台", // 安装位置（可选）
  "description": "设备描述"      // 设备描述（可选）
}
```

### 4. 更新设备信息

**接口**: `PUT /admin/device/{id}`

**描述**: 更新设备基本信息

**权限**: `device:update`

### 5. 删除设备

**接口**: `DELETE /admin/device/{id}`

**描述**: 删除设备（软删除）

**权限**: `device:delete`

### 6. 批量删除设备

**接口**: `DELETE /admin/device/batch`

**描述**: 批量删除设备

**权限**: `device:delete`

**请求体**:
```json
[1, 2, 3]  // 设备ID数组
```

## 设备状态管理

### 7. 更新设备状态

**接口**: `PUT /admin/device/{id}/status`

**描述**: 启用或禁用设备

**权限**: `device:status`

**路径参数**:
- `id` (Long): 设备ID

**请求参数**:
- `status` (Integer): 状态，1-启用 0-禁用

### 8. 设备上线

**接口**: `POST /admin/device/{id}/online`

**描述**: 手动设置设备上线

**权限**: `device:online`

### 9. 设备下线

**接口**: `POST /admin/device/{id}/offline`

**描述**: 手动设置设备下线

**权限**: `device:offline`

### 10. 重启设备

**接口**: `POST /admin/device/{id}/restart`

**描述**: 远程重启设备

**权限**: `device:restart`

### 11. 获取设备状态

**接口**: `GET /admin/device/{id}/status`

**描述**: 获取设备实时状态信息

**权限**: `device:status:view`

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "deviceId": 1,
    "onlineStatus": 1,
    "cpuUsage": 45.2,
    "memoryUsage": 67.8,
    "diskUsage": 23.5,
    "temperature": 42.3,
    "networkStatus": 1,
    "lastHeartbeatTime": "2024-01-01T10:00:00",
    "uptime": 86400,
    "errorCount": 0
  }
}
```

## 设备配置管理

### 12. 获取设备配置

**接口**: `GET /admin/device/{id}/config`

**描述**: 获取设备配置信息

**权限**: `device:config:view`

### 13. 更新设备配置

**接口**: `PUT /admin/device/{id}/config`

**描述**: 更新设备配置

**权限**: `device:config:update`

**请求体**:
```json
{
  "scanInterval": 1000,         // 扫描间隔（毫秒）
  "heartbeatInterval": 30000,   // 心跳间隔（毫秒）
  "autoRestart": true,          // 自动重启
  "logLevel": "INFO",           // 日志级别
  "maxRetries": 3,              // 最大重试次数
  "timeout": 5000,              // 超时时间（毫秒）
  "customConfig": {             // 自定义配置
    "key1": "value1",
    "key2": "value2"
  }
}
```

### 14. 推送配置到设备

**接口**: `POST /admin/device/{id}/config/push`

**描述**: 将配置推送到设备

**权限**: `device:config:push`

### 15. 获取配置模板

**接口**: `GET /admin/device/config/template`

**描述**: 获取设备配置模板

**权限**: `device:config:template`

**请求参数**:
- `deviceType` (String): 设备类型

## 设备监控

### 16. 获取设备监控数据

**接口**: `GET /admin/device/{id}/monitor`

**描述**: 获取设备监控数据

**权限**: `device:monitor`

**请求参数**:
- `startTime` (String): 开始时间
- `endTime` (String): 结束时间
- `interval` (String): 时间间隔（5m, 1h, 1d）

### 17. 获取设备日志

**接口**: `GET /admin/device/{id}/logs`

**描述**: 获取设备运行日志

**权限**: `device:logs`

**请求参数**:
- `level` (String): 日志级别（可选）
- `startTime` (String): 开始时间（可选）
- `endTime` (String): 结束时间（可选）
- `current` (Integer): 页码
- `size` (Integer): 每页数量

### 18. 获取设备告警

**接口**: `GET /admin/device/{id}/alerts`

**描述**: 获取设备告警信息

**权限**: `device:alerts`

## 设备维护

### 19. 设备固件升级

**接口**: `POST /admin/device/{id}/firmware/upgrade`

**描述**: 升级设备固件

**权限**: `device:firmware:upgrade`

**请求体**:
```json
{
  "firmwareUrl": "http://example.com/firmware.bin",
  "version": "v1.1.0",
  "forceUpgrade": false
}
```

### 20. 获取固件版本列表

**接口**: `GET /admin/device/firmware/versions`

**描述**: 获取可用的固件版本列表

**权限**: `device:firmware:list`

### 21. 设备诊断

**接口**: `POST /admin/device/{id}/diagnose`

**描述**: 执行设备诊断

**权限**: `device:diagnose`

### 22. 清理设备缓存

**接口**: `POST /admin/device/{id}/cache/clear`

**描述**: 清理设备缓存

**权限**: `device:cache:clear`

## 批量操作

### 23. 批量更新设备状态

**接口**: `PUT /admin/device/batch/status`

**描述**: 批量更新设备状态

**权限**: `device:batch:status`

### 24. 批量推送配置

**接口**: `POST /admin/device/batch/config/push`

**描述**: 批量推送配置到设备

**权限**: `device:batch:config`

### 25. 批量固件升级

**接口**: `POST /admin/device/batch/firmware/upgrade`

**描述**: 批量升级设备固件

**权限**: `device:batch:firmware`

## 统计报表

### 26. 设备统计概览

**接口**: `GET /admin/device/statistics/overview`

**描述**: 获取设备统计概览

**权限**: `device:statistics`

### 27. 设备类型统计

**接口**: `GET /admin/device/statistics/type`

**描述**: 按设备类型统计

**权限**: `device:statistics`

### 28. 设备状态统计

**接口**: `GET /admin/device/statistics/status`

**描述**: 按设备状态统计

**权限**: `device:statistics`

## 设备类型说明

| 类型 | 说明 |
|------|------|
| SCANNER | 扫码设备 |
| PRINTER | 打印设备 |
| DISPLAY | 显示设备 |
| SENSOR | 传感器设备 |
| CAMERA | 摄像设备 |
| GATEWAY | 网关设备 |

## 设备状态说明

| 状态 | 说明 |
|------|------|
| 0 | 禁用 |
| 1 | 启用 |
| 2 | 维护中 |
| 3 | 故障 |

## 在线状态说明

| 状态 | 说明 |
|------|------|
| 0 | 离线 |
| 1 | 在线 |
| 2 | 异常 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 500001 | 设备编码已存在 |
| 500002 | 设备不存在 |
| 500003 | 设备正在使用中，无法删除 |
| 500004 | 设备离线，无法执行操作 |
| 500005 | 固件升级失败 |
| 500006 | 配置推送失败 |

## 注意事项

1. 设备编码必须唯一
2. 删除设备前需确保设备已下线
3. 固件升级过程中设备可能会重启
4. 配置推送需要设备在线
5. 设备监控数据保留30天
6. 批量操作有数量限制（最多100个设备）
