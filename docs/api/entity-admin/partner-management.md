# 合作商管理 API

## 概述
合作商管理模块为业务主体管理员提供对下级合作商的完整管理功能，包括合作商的注册、审核、配置、监控等。

**基础路径**: `/entity/partner`

**权限要求**: 业务主体管理员角色 (`ENTITY_ADMIN`)

**数据权限**: 只能管理本业务主体下的合作商

## 合作商基础管理

### 1. 分页查询合作商列表

**接口**: `GET /entity/partner/page`

**描述**: 分页查询当前业务主体下的合作商列表

**权限**: `partner:list`

**请求参数**:
```json
{
  "current": 1,              // 页码，默认1
  "size": 10,               // 每页数量，默认10
  "partnerName": "合作商A",  // 合作商名称（可选）
  "contactName": "张三",     // 联系人姓名（可选）
  "contactPhone": "138",     // 联系人电话（可选）
  "status": 1,              // 状态：1-正常 0-禁用（可选）
  "auditStatus": 1,         // 审核状态：0-待审核 1-已通过 2-已拒绝（可选）
  "province": "广东省",      // 省份（可选）
  "city": "深圳市",         // 城市（可选）
  "createTimeStart": "2024-01-01", // 创建时间开始（可选）
  "createTimeEnd": "2024-12-31"    // 创建时间结束（可选）
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "partnerName": "合作商A",
        "partnerCode": "PA001",
        "contactName": "张三",
        "contactPhone": "***********",
        "contactEmail": "<EMAIL>",
        "province": "广东省",
        "city": "深圳市",
        "district": "南山区",
        "address": "科技园南区",
        "status": 1,
        "auditStatus": 1,
        "shopCount": 5,
        "deviceCount": 15,
        "createTime": "2024-01-01T00:00:00",
        "updateTime": "2024-01-01T10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 获取合作商详情

**接口**: `GET /entity/partner/{id}`

**描述**: 根据ID获取合作商详细信息

**权限**: `partner:detail`

**路径参数**:
- `id` (Long): 合作商ID

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "partnerName": "合作商A",
    "partnerCode": "PA001",
    "businessLicense": "91440300123456789X",
    "legalPerson": "李四",
    "contactName": "张三",
    "contactPhone": "***********",
    "contactEmail": "<EMAIL>",
    "province": "广东省",
    "city": "深圳市",
    "district": "南山区",
    "address": "科技园南区A座1001",
    "businessScope": "技术服务",
    "registeredCapital": "100万",
    "establishDate": "2020-01-01",
    "status": 1,
    "auditStatus": 1,
    "auditTime": "2024-01-01T10:00:00",
    "auditRemark": "审核通过",
    "commissionRate": 15.5,
    "settlementCycle": 7,
    "description": "合作商描述",
    "createTime": "2024-01-01T00:00:00",
    "updateTime": "2024-01-01T10:00:00"
  }
}
```

### 3. 创建合作商

**接口**: `POST /entity/partner`

**描述**: 创建新的合作商

**权限**: `partner:create`

**请求体**:
```json
{
  "partnerName": "新合作商",           // 合作商名称（必填）
  "partnerCode": "PA002",            // 合作商编码（可选，系统自动生成）
  "businessLicense": "91440300987654321Y", // 营业执照号（必填）
  "legalPerson": "王五",             // 法人代表（必填）
  "contactName": "赵六",             // 联系人姓名（必填）
  "contactPhone": "***********",     // 联系人电话（必填）
  "contactEmail": "<EMAIL>", // 联系人邮箱（可选）
  "province": "广东省",              // 省份（必填）
  "city": "广州市",                 // 城市（必填）
  "district": "天河区",             // 区县（必填）
  "address": "珠江新城B座2001",      // 详细地址（必填）
  "businessScope": "零售服务",       // 经营范围（可选）
  "registeredCapital": "200万",      // 注册资本（可选）
  "establishDate": "2021-01-01",     // 成立日期（可选）
  "commissionRate": 12.0,           // 分成比例（可选）
  "settlementCycle": 15,            // 结算周期（天）（可选）
  "description": "合作商描述"        // 描述（可选）
}
```

### 4. 更新合作商信息

**接口**: `PUT /entity/partner/{id}`

**描述**: 更新合作商基本信息

**权限**: `partner:update`

### 5. 删除合作商

**接口**: `DELETE /entity/partner/{id}`

**描述**: 删除合作商（软删除）

**权限**: `partner:delete`

**注意**: 只能删除没有门店和设备的合作商

### 6. 批量删除合作商

**接口**: `DELETE /entity/partner/batch`

**描述**: 批量删除合作商

**权限**: `partner:delete`

## 合作商审核管理

### 7. 审核合作商

**接口**: `PUT /entity/partner/{id}/audit`

**描述**: 审核合作商申请

**权限**: `partner:audit`

**请求体**:
```json
{
  "auditStatus": 1,          // 审核状态：1-通过 2-拒绝
  "auditRemark": "审核通过"   // 审核备注（可选）
}
```

### 8. 获取待审核合作商列表

**接口**: `GET /entity/partner/pending`

**描述**: 获取待审核的合作商列表

**权限**: `partner:audit`

### 9. 批量审核合作商

**接口**: `PUT /entity/partner/batch/audit`

**描述**: 批量审核合作商

**权限**: `partner:audit`

**请求体**:
```json
{
  "partnerIds": [1, 2, 3],   // 合作商ID列表
  "auditStatus": 1,          // 审核状态
  "auditRemark": "批量审核通过"
}
```

## 合作商状态管理

### 10. 更新合作商状态

**接口**: `PUT /entity/partner/{id}/status`

**描述**: 启用或禁用合作商

**权限**: `partner:status`

**请求参数**:
- `status` (Integer): 状态，1-启用 0-禁用

### 11. 冻结合作商

**接口**: `POST /entity/partner/{id}/freeze`

**描述**: 冻结合作商账户

**权限**: `partner:freeze`

**请求体**:
```json
{
  "reason": "违规操作",      // 冻结原因（必填）
  "duration": 30            // 冻结天数（可选，0表示永久冻结）
}
```

### 12. 解冻合作商

**接口**: `POST /entity/partner/{id}/unfreeze`

**描述**: 解冻合作商账户

**权限**: `partner:unfreeze`

## 合作商配置管理

### 13. 获取合作商配置

**接口**: `GET /entity/partner/{id}/config`

**描述**: 获取合作商配置信息

**权限**: `partner:config:view`

### 14. 更新合作商配置

**接口**: `PUT /entity/partner/{id}/config`

**描述**: 更新合作商配置

**权限**: `partner:config:update`

**请求体**:
```json
{
  "commissionRate": 15.0,    // 分成比例
  "settlementCycle": 7,      // 结算周期（天）
  "maxShopCount": 10,        // 最大门店数量
  "maxDeviceCount": 50,      // 最大设备数量
  "allowSelfRegister": true, // 允许自主注册门店
  "autoAuditShop": false,    // 自动审核门店
  "customConfig": {          // 自定义配置
    "key1": "value1",
    "key2": "value2"
  }
}
```

### 15. 重置合作商配置

**接口**: `POST /entity/partner/{id}/config/reset`

**描述**: 重置合作商配置为默认值

**权限**: `partner:config:reset`

## 合作商门店管理

### 16. 获取合作商门店列表

**接口**: `GET /entity/partner/{id}/shops`

**描述**: 获取指定合作商下的门店列表

**权限**: `partner:shop:list`

### 17. 为合作商创建门店

**接口**: `POST /entity/partner/{id}/shops`

**描述**: 为指定合作商创建门店

**权限**: `partner:shop:create`

### 18. 获取合作商设备列表

**接口**: `GET /entity/partner/{id}/devices`

**描述**: 获取指定合作商下的设备列表

**权限**: `partner:device:list`

## 合作商财务管理

### 19. 获取合作商财务概览

**接口**: `GET /entity/partner/{id}/finance/overview`

**描述**: 获取合作商财务概览信息

**权限**: `partner:finance:view`

### 20. 获取合作商分成记录

**接口**: `GET /entity/partner/{id}/finance/commission`

**描述**: 获取合作商分成记录

**权限**: `partner:finance:commission`

### 21. 获取合作商提现记录

**接口**: `GET /entity/partner/{id}/finance/withdraw`

**描述**: 获取合作商提现记录

**权限**: `partner:finance:withdraw`

## 统计报表

### 22. 合作商统计概览

**接口**: `GET /entity/partner/statistics/overview`

**描述**: 获取合作商统计概览

**权限**: `partner:statistics`

### 23. 合作商业绩统计

**接口**: `GET /entity/partner/statistics/performance`

**描述**: 获取合作商业绩统计

**权限**: `partner:statistics`

**请求参数**:
- `startDate` (String): 开始日期
- `endDate` (String): 结束日期
- `groupBy` (String): 分组方式（day, week, month）

### 24. 合作商排行榜

**接口**: `GET /entity/partner/statistics/ranking`

**描述**: 获取合作商排行榜

**权限**: `partner:statistics`

**请求参数**:
- `rankBy` (String): 排序字段（revenue, order_count, shop_count）
- `period` (String): 统计周期（week, month, quarter, year）
- `limit` (Integer): 返回数量，默认10

## 合作商导入导出

### 25. 导出合作商列表

**接口**: `GET /entity/partner/export`

**描述**: 导出合作商列表到Excel

**权限**: `partner:export`

### 26. 导入合作商

**接口**: `POST /entity/partner/import`

**描述**: 批量导入合作商

**权限**: `partner:import`

### 27. 下载导入模板

**接口**: `GET /entity/partner/import/template`

**描述**: 下载合作商导入模板

**权限**: `partner:import`

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 600001 | 合作商名称已存在 |
| 600002 | 营业执照号已存在 |
| 600003 | 合作商不存在 |
| 600004 | 合作商状态不允许此操作 |
| 600005 | 合作商下存在门店，无法删除 |
| 600006 | 合作商下存在设备，无法删除 |
| 600007 | 分成比例超出允许范围 |
| 600008 | 结算周期超出允许范围 |

## 注意事项

1. 业务主体管理员只能管理本业务主体下的合作商
2. 合作商名称在同一业务主体下必须唯一
3. 营业执照号在全系统内必须唯一
4. 删除合作商前需确保下面没有门店和设备
5. 分成比例范围：0-50%
6. 结算周期范围：1-30天
7. 合作商审核通过后才能正常使用系统功能
8. 冻结的合作商无法进行业务操作
9. 导入合作商时会自动验证数据格式和业务规则
10. 合作商配置修改后立即生效
