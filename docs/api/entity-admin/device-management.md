# 业务主体设备管理 API

## 概述
业务主体设备管理模块为业务主体管理员提供对本业务主体下所有设备的管理功能，包括设备的查看、配置、监控等。

**基础路径**: `/entity/device`

**权限要求**: 业务主体管理员角色 (`ENTITY_ADMIN`)

**数据权限**: 只能管理本业务主体下的设备

## 设备查询管理

### 1. 分页查询设备列表

**接口**: `GET /entity/device/page`

**描述**: 分页查询当前业务主体下的设备列表

**权限**: `entity:device:list`

**请求参数**:
```json
{
  "current": 1,              // 页码，默认1
  "size": 10,               // 每页数量，默认10
  "deviceCode": "DEV001",   // 设备编码（可选）
  "deviceName": "设备1",     // 设备名称（可选）
  "deviceType": "SCANNER",  // 设备类型（可选）
  "status": 1,              // 设备状态（可选）
  "partnerId": 1,           // 合作商ID（可选）
  "shopId": 1,              // 门店ID（可选）
  "onlineStatus": 1,        // 在线状态：1-在线 0-离线（可选）
  "installLocation": "收银台", // 安装位置（可选）
  "createTimeStart": "2024-01-01", // 创建时间开始（可选）
  "createTimeEnd": "2024-12-31"    // 创建时间结束（可选）
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "deviceCode": "DEV001",
        "deviceName": "扫码设备1",
        "deviceType": "SCANNER",
        "model": "SC-2024",
        "status": 1,
        "onlineStatus": 1,
        "partnerId": 1,
        "partnerName": "合作商A",
        "shopId": 1,
        "shopName": "门店A",
        "installLocation": "门店入口",
        "lastHeartbeatTime": "2024-01-01T10:00:00",
        "createTime": "2024-01-01T00:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 获取设备详情

**接口**: `GET /entity/device/{id}`

**描述**: 根据ID获取设备详细信息

**权限**: `entity:device:detail`

**路径参数**:
- `id` (Long): 设备ID

### 3. 按合作商查询设备

**接口**: `GET /entity/device/partner/{partnerId}`

**描述**: 查询指定合作商下的设备列表

**权限**: `entity:device:list`

### 4. 按门店查询设备

**接口**: `GET /entity/device/shop/{shopId}`

**描述**: 查询指定门店下的设备列表

**权限**: `entity:device:list`

### 5. 设备搜索

**接口**: `GET /entity/device/search`

**描述**: 根据关键词搜索设备

**权限**: `entity:device:search`

**请求参数**:
- `keyword` (String): 搜索关键词
- `searchType` (String): 搜索类型（code, name, location）

## 设备状态监控

### 6. 获取设备状态概览

**接口**: `GET /entity/device/status/overview`

**描述**: 获取设备状态统计概览

**权限**: `entity:device:monitor`

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalCount": 100,        // 设备总数
    "onlineCount": 85,        // 在线设备数
    "offlineCount": 10,       // 离线设备数
    "errorCount": 5,          // 故障设备数
    "onlineRate": 85.0,       // 在线率
    "byType": {               // 按类型统计
      "SCANNER": 60,
      "PRINTER": 25,
      "DISPLAY": 15
    },
    "byStatus": {             // 按状态统计
      "NORMAL": 85,
      "OFFLINE": 10,
      "ERROR": 5
    }
  }
}
```

### 7. 获取设备实时状态

**接口**: `GET /entity/device/{id}/status`

**描述**: 获取设备实时状态信息

**权限**: `entity:device:monitor`

### 8. 获取离线设备列表

**接口**: `GET /entity/device/offline`

**描述**: 获取离线设备列表

**权限**: `entity:device:monitor`

### 9. 获取故障设备列表

**接口**: `GET /entity/device/error`

**描述**: 获取故障设备列表

**权限**: `entity:device:monitor`

## 设备配置管理

### 10. 获取设备配置

**接口**: `GET /entity/device/{id}/config`

**描述**: 获取设备配置信息

**权限**: `entity:device:config:view`

### 11. 更新设备配置

**接口**: `PUT /entity/device/{id}/config`

**描述**: 更新设备配置（仅限业务配置）

**权限**: `entity:device:config:update`

**请求体**:
```json
{
  "businessConfig": {        // 业务配置
    "scanTimeout": 5000,     // 扫描超时时间
    "displayDuration": 3000, // 显示持续时间
    "autoRestart": true,     // 自动重启
    "workingHours": {        // 工作时间
      "start": "08:00",
      "end": "22:00"
    }
  },
  "customConfig": {          // 自定义配置
    "welcomeMessage": "欢迎使用",
    "theme": "default"
  }
}
```

### 12. 批量更新设备配置

**接口**: `PUT /entity/device/batch/config`

**描述**: 批量更新设备配置

**权限**: `entity:device:config:batch`

### 13. 获取配置模板

**接口**: `GET /entity/device/config/template`

**描述**: 获取设备配置模板

**权限**: `entity:device:config:template`

## 设备操作管理

### 14. 重启设备

**接口**: `POST /entity/device/{id}/restart`

**描述**: 远程重启设备

**权限**: `entity:device:restart`

### 15. 批量重启设备

**接口**: `POST /entity/device/batch/restart`

**描述**: 批量重启设备

**权限**: `entity:device:restart`

**请求体**:
```json
{
  "deviceIds": [1, 2, 3],    // 设备ID列表
  "reason": "配置更新"       // 重启原因（可选）
}
```

### 16. 设备诊断

**接口**: `POST /entity/device/{id}/diagnose`

**描述**: 执行设备诊断

**权限**: `entity:device:diagnose`

### 17. 清理设备缓存

**接口**: `POST /entity/device/{id}/cache/clear`

**描述**: 清理设备缓存

**权限**: `entity:device:cache`

## 设备监控数据

### 18. 获取设备监控数据

**接口**: `GET /entity/device/{id}/monitor`

**描述**: 获取设备监控数据

**权限**: `entity:device:monitor`

**请求参数**:
- `startTime` (String): 开始时间
- `endTime` (String): 结束时间
- `metrics` (String): 监控指标（cpu, memory, network）

### 19. 获取设备使用统计

**接口**: `GET /entity/device/{id}/usage`

**描述**: 获取设备使用统计

**权限**: `entity:device:usage`

### 20. 获取设备告警

**接口**: `GET /entity/device/{id}/alerts`

**描述**: 获取设备告警信息

**权限**: `entity:device:alerts`

### 21. 处理设备告警

**接口**: `PUT /entity/device/alert/{alertId}/handle`

**描述**: 处理设备告警

**权限**: `entity:device:alert:handle`

**请求体**:
```json
{
  "handleResult": "已处理",  // 处理结果
  "handleRemark": "重启设备解决" // 处理备注
}
```

## 设备日志管理

### 22. 获取设备日志

**接口**: `GET /entity/device/{id}/logs`

**描述**: 获取设备运行日志

**权限**: `entity:device:logs`

**请求参数**:
- `level` (String): 日志级别（INFO, WARN, ERROR）
- `startTime` (String): 开始时间
- `endTime` (String): 结束时间
- `current` (Integer): 页码
- `size` (Integer): 每页数量

### 23. 导出设备日志

**接口**: `GET /entity/device/{id}/logs/export`

**描述**: 导出设备日志

**权限**: `entity:device:logs:export`

## 设备统计报表

### 24. 设备使用率统计

**接口**: `GET /entity/device/statistics/usage`

**描述**: 获取设备使用率统计

**权限**: `entity:device:statistics`

### 25. 设备故障统计

**接口**: `GET /entity/device/statistics/error`

**描述**: 获取设备故障统计

**权限**: `entity:device:statistics`

### 26. 设备性能统计

**接口**: `GET /entity/device/statistics/performance`

**描述**: 获取设备性能统计

**权限**: `entity:device:statistics`

### 27. 设备分布统计

**接口**: `GET /entity/device/statistics/distribution`

**描述**: 获取设备分布统计

**权限**: `entity:device:statistics`

## 设备导出功能

### 28. 导出设备列表

**接口**: `GET /entity/device/export`

**描述**: 导出设备列表到Excel

**权限**: `entity:device:export`

### 29. 导出设备监控报告

**接口**: `GET /entity/device/export/monitor`

**描述**: 导出设备监控报告

**权限**: `entity:device:export`

## 设备权限说明

业务主体管理员对设备的权限范围：

| 操作类型 | 权限范围 | 说明 |
|----------|----------|------|
| 查看 | 本业务主体下所有设备 | 可查看设备基本信息、状态、配置 |
| 配置 | 业务配置部分 | 可修改业务相关配置，不能修改系统配置 |
| 操作 | 重启、诊断、缓存清理 | 可执行基本运维操作 |
| 监控 | 状态监控、日志查看 | 可查看设备运行状态和日志 |
| 限制 | 不能删除、不能修改硬件配置 | 设备的创建和删除由系统管理员负责 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 700001 | 设备不存在或无权限访问 |
| 700002 | 设备离线，无法执行操作 |
| 700003 | 设备配置格式错误 |
| 700004 | 设备正在执行其他操作 |
| 700005 | 设备诊断失败 |
| 700006 | 配置推送失败 |

## 注意事项

1. 业务主体管理员只能管理本业务主体下的设备
2. 不能创建或删除设备，只能管理已分配的设备
3. 只能修改业务相关配置，不能修改系统级配置
4. 设备操作需要设备在线才能执行
5. 监控数据保留30天
6. 批量操作有数量限制（最多50个设备）
7. 设备配置修改后需要推送到设备才能生效
8. 重要操作会记录操作日志
