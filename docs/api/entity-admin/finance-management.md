# 财务管理 API

## 概述
财务管理模块为业务主体管理员提供财务数据查看、分成管理、提现审核等功能。

**基础路径**: `/entity/finance`

**权限要求**: 业务主体管理员角色 (`ENTITY_ADMIN`)

**数据权限**: 只能查看本业务主体的财务数据

## 财务概览

### 1. 获取财务概览

**接口**: `GET /entity/finance/overview`

**描述**: 获取业务主体财务概览信息

**权限**: `entity:finance:overview`

**请求参数**:
- `period` (String): 统计周期（today, week, month, year）

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalRevenue": 1000000.00,      // 总收入
    "totalCommission": 150000.00,    // 总分成
    "availableBalance": 50000.00,    // 可用余额
    "frozenBalance": 5000.00,        // 冻结余额
    "pendingWithdraw": 10000.00,     // 待提现金额
    "todayRevenue": 5000.00,         // 今日收入
    "monthRevenue": 80000.00,        // 本月收入
    "revenueGrowth": 15.5,           // 收入增长率(%)
    "commissionRate": 15.0,          // 平均分成比例(%)
    "lastUpdateTime": "2024-01-01T10:00:00"
  }
}
```

### 2. 获取财务趋势

**接口**: `GET /entity/finance/trend`

**描述**: 获取财务数据趋势

**权限**: `entity:finance:trend`

**请求参数**:
- `startDate` (String): 开始日期
- `endDate` (String): 结束日期
- `granularity` (String): 粒度（day, week, month）

## 账户管理

### 3. 获取账户信息

**接口**: `GET /entity/finance/account`

**描述**: 获取业务主体账户信息

**权限**: `entity:finance:account`

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "accountId": 1,
    "accountType": "ENTITY",
    "availableBalance": 50000.00,
    "frozenBalance": 5000.00,
    "totalBalance": 55000.00,
    "creditLimit": 100000.00,
    "status": 1,
    "createTime": "2024-01-01T00:00:00",
    "lastTransactionTime": "2024-01-01T10:00:00"
  }
}
```

### 4. 获取账户流水

**接口**: `GET /entity/finance/account/transactions`

**描述**: 获取账户流水记录

**权限**: `entity:finance:transaction`

**请求参数**:
```json
{
  "current": 1,              // 页码
  "size": 20,               // 每页数量
  "transactionType": 1,     // 交易类型（可选）
  "startDate": "2024-01-01", // 开始日期（可选）
  "endDate": "2024-01-31",   // 结束日期（可选）
  "minAmount": 100.00,      // 最小金额（可选）
  "maxAmount": 10000.00     // 最大金额（可选）
}
```

## 分成管理

### 5. 分页查询分成记录

**接口**: `GET /entity/finance/commission/page`

**描述**: 分页查询分成记录

**权限**: `entity:finance:commission`

**请求参数**:
```json
{
  "current": 1,              // 页码
  "size": 20,               // 每页数量
  "orderId": "ORD001",      // 订单号（可选）
  "partnerId": 1,           // 合作商ID（可选）
  "shopId": 1,              // 门店ID（可选）
  "startDate": "2024-01-01", // 开始日期（可选）
  "endDate": "2024-01-31",   // 结束日期（可选）
  "minAmount": 10.00,       // 最小金额（可选）
  "maxAmount": 1000.00      // 最大金额（可选）
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "orderId": "ORD001",
        "orderAmount": 100.00,
        "platformAmount": 30.00,
        "entityAmount": 40.00,
        "partnerAmount": 20.00,
        "shopAmount": 10.00,
        "partnerId": 1,
        "partnerName": "合作商A",
        "shopId": 1,
        "shopName": "门店A",
        "createTime": "2024-01-01T10:00:00"
      }
    ],
    "total": 100,
    "size": 20,
    "current": 1,
    "pages": 5
  }
}
```

### 6. 获取分成统计

**接口**: `GET /entity/finance/commission/statistics`

**描述**: 获取分成统计数据

**权限**: `entity:finance:commission:stats`

### 7. 导出分成记录

**接口**: `GET /entity/finance/commission/export`

**描述**: 导出分成记录到Excel

**权限**: `entity:finance:commission:export`

## 提现管理

### 8. 分页查询提现记录

**接口**: `GET /entity/finance/withdraw/page`

**描述**: 分页查询提现记录

**权限**: `entity:finance:withdraw`

**请求参数**:
```json
{
  "current": 1,              // 页码
  "size": 20,               // 每页数量
  "withdrawNo": "WD001",    // 提现单号（可选）
  "status": 1,              // 提现状态（可选）
  "startDate": "2024-01-01", // 开始日期（可选）
  "endDate": "2024-01-31",   // 结束日期（可选）
  "minAmount": 100.00,      // 最小金额（可选）
  "maxAmount": 10000.00     // 最大金额（可选）
}
```

### 9. 获取提现详情

**接口**: `GET /entity/finance/withdraw/{id}`

**描述**: 获取提现详情

**权限**: `entity:finance:withdraw:detail`

### 10. 审核提现申请

**接口**: `PUT /entity/finance/withdraw/{id}/audit`

**描述**: 审核下级的提现申请

**权限**: `entity:finance:withdraw:audit`

**请求体**:
```json
{
  "auditStatus": 1,          // 审核状态：1-通过 2-拒绝
  "auditRemark": "审核通过"   // 审核备注
}
```

### 11. 批量审核提现

**接口**: `PUT /entity/finance/withdraw/batch/audit`

**描述**: 批量审核提现申请

**权限**: `entity:finance:withdraw:audit`

### 12. 获取待审核提现列表

**接口**: `GET /entity/finance/withdraw/pending`

**描述**: 获取待审核的提现申请

**权限**: `entity:finance:withdraw:audit`

## 对账管理

### 13. 获取对账概览

**接口**: `GET /entity/finance/reconciliation/overview`

**描述**: 获取对账概览信息

**权限**: `entity:finance:reconciliation`

### 14. 生成对账单

**接口**: `POST /entity/finance/reconciliation/generate`

**描述**: 生成指定期间的对账单

**权限**: `entity:finance:reconciliation:generate`

**请求体**:
```json
{
  "startDate": "2024-01-01",  // 开始日期
  "endDate": "2024-01-31",    // 结束日期
  "partnerId": 1,             // 合作商ID（可选）
  "reconciliationType": "MONTHLY" // 对账类型
}
```

### 15. 获取对账单列表

**接口**: `GET /entity/finance/reconciliation/page`

**描述**: 分页查询对账单

**权限**: `entity:finance:reconciliation`

### 16. 下载对账单

**接口**: `GET /entity/finance/reconciliation/{id}/download`

**描述**: 下载对账单文件

**权限**: `entity:finance:reconciliation:download`

## 财务报表

### 17. 收入报表

**接口**: `GET /entity/finance/report/revenue`

**描述**: 获取收入报表

**权限**: `entity:finance:report`

**请求参数**:
- `startDate` (String): 开始日期
- `endDate` (String): 结束日期
- `groupBy` (String): 分组方式（partner, shop, day, month）

### 18. 分成报表

**接口**: `GET /entity/finance/report/commission`

**描述**: 获取分成报表

**权限**: `entity:finance:report`

### 19. 提现报表

**接口**: `GET /entity/finance/report/withdraw`

**描述**: 获取提现报表

**权限**: `entity:finance:report`

### 20. 财务汇总报表

**接口**: `GET /entity/finance/report/summary`

**描述**: 获取财务汇总报表

**权限**: `entity:finance:report`

## 财务配置

### 21. 获取财务配置

**接口**: `GET /entity/finance/config`

**描述**: 获取财务相关配置

**权限**: `entity:finance:config`

### 22. 更新财务配置

**接口**: `PUT /entity/finance/config`

**描述**: 更新财务配置

**权限**: `entity:finance:config:update`

**请求体**:
```json
{
  "autoAuditWithdraw": false,    // 自动审核提现
  "maxWithdrawAmount": 50000.00, // 最大提现金额
  "minWithdrawAmount": 100.00,   // 最小提现金额
  "withdrawFeeRate": 0.5,        // 提现手续费率(%)
  "settlementCycle": 7,          // 结算周期（天）
  "autoSettlement": true,        // 自动结算
  "riskControlEnabled": true     // 风控开启
}
```

## 数据校验

### 23. 财务数据校验

**接口**: `POST /entity/finance/validation/check`

**描述**: 执行财务数据一致性校验

**权限**: `entity:finance:validation`

**请求体**:
```json
{
  "startDate": "2024-01-01",     // 开始日期
  "endDate": "2024-01-31",       // 结束日期
  "checkType": "COMMISSION"      // 校验类型
}
```

### 24. 获取校验报告

**接口**: `GET /entity/finance/validation/report`

**描述**: 获取数据校验报告

**权限**: `entity:finance:validation`

## 财务状态说明

### 提现状态
| 状态 | 说明 |
|------|------|
| 0 | 申请中 |
| 1 | 审核通过 |
| 2 | 审核拒绝 |
| 3 | 已支付 |
| 4 | 支付失败 |
| 5 | 已取消 |

### 交易类型
| 类型 | 说明 |
|------|------|
| 1 | 收入 |
| 2 | 提现 |
| 3 | 退款 |
| 4 | 系统调整 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 800001 | 账户余额不足 |
| 800002 | 提现金额超出限制 |
| 800003 | 提现申请不存在 |
| 800004 | 提现状态不允许此操作 |
| 800005 | 分成记录不存在 |
| 800006 | 对账单生成失败 |
| 800007 | 财务配置更新失败 |

## 注意事项

1. 业务主体管理员只能查看本业务主体的财务数据
2. 提现审核权限仅限于下级合作商的提现申请
3. 财务数据具有时效性，建议定期校验
4. 大额提现需要额外审核流程
5. 财务报表数据可能有延迟，以实际到账为准
6. 分成比例修改需要系统管理员权限
7. 对账单生成后不可修改
8. 财务操作会记录详细的审计日志
