# 今夜城堡项目架构优化方案

## 1. 现状分析

### 1.1 模块架构现状

通过深入分析项目代码，发现以下模块结构：

```
modules/
├── admin/          # 管理员模块
├── api/            # API模块
├── auth/           # 认证模块
├── common/         # 公共模块
├── device/         # 设备管理模块
├── entity/         # 业务主体模块
├── feedback/       # 反馈模块
├── finance/        # 财务模块
├── menu/           # 菜单模块
├── order/          # 订单模块
├── partner/        # 合作商模块
├── shop/           # 门店模块
├── statistics/     # 统计模块
├── system/         # 系统模块
└── user/           # 用户模块
```

### 1.2 权限体系分析

项目采用了Sa-Token + 自定义数据权限的多层级权限控制：

**角色层级**：
- `system` - 系统管理员（全局权限）
- `entity` - 业务主体管理员（业务主体级权限）
- `partner` - 合作商管理员（合作商级权限）
- `shop` - 门店管理员（门店级权限）

**权限控制机制**：
- `@SaCheckLogin` - 登录验证
- `@SaCheckPermission` - 功能权限验证
- `@DataPermission` - 数据权限控制
- `DataPermissionInterceptor` - SQL级别的数据过滤

### 1.3 现有优化实践

项目已经实现了部分优化：

1. **BaseFinanceController** - 财务模块的基础控制器
2. **BaseDeviceController** - 设备模块的基础控制器
3. **DataPermissionContext** - 数据权限上下文管理
4. **统一的权限注解体系**

### 1.4 存在的问题

1. **代码重复**：不同角色的控制器存在大量相似的CRUD操作
2. **权限逻辑分散**：权限验证逻辑在各个控制器中重复实现
3. **缺乏统一抽象**：除了财务和设备模块，其他模块缺乏基础控制器
4. **离线设备处理不统一**：设备离线状态的处理逻辑分散
5. **服务层抽象不足**：缺乏通用的服务层接口

## 2. 架构优化方案

### 2.1 控制器层重构方案

#### 2.1.1 通用基础控制器设计

参考AdminAuthController的设计模式，创建通用的基础控制器：

```java
/**
 * 通用CRUD基础控制器
 * @param <T> 实体类型
 * @param <VO> 视图对象类型
 * @param <DTO> 数据传输对象类型
 */
@Slf4j
public abstract class BaseCrudController<T, VO, DTO> {
    
    // 通用CRUD操作
    @GetMapping("/page")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<PageResult<VO>> page(@Valid PageQueryDTO pageQuery) {
        // 权限验证
        validateListPermission(pageQuery);
        
        // 执行查询
        PageResult<VO> result = getFilteredPageResult(pageQuery);
        return CommonResult.success(result);
    }
    
    @GetMapping("/{id}")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<VO> getById(@PathVariable Long id) {
        VO entity = getFilteredById(id);
        validateEntityPermission(entity);
        return CommonResult.success(entity);
    }
    
    @PostMapping
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<VO> create(@Valid @RequestBody DTO dto) {
        validateCreatePermission(dto);
        preprocessCreate(dto);
        VO result = executeCreate(dto);
        return CommonResult.success(result);
    }
    
    // 抽象方法，子类实现
    protected abstract PageResult<VO> getFilteredPageResult(PageQueryDTO pageQuery);
    protected abstract VO getFilteredById(Long id);
    protected abstract void validateListPermission(PageQueryDTO pageQuery);
    protected abstract void validateEntityPermission(VO entity);
    protected abstract void validateCreatePermission(DTO dto);
    protected abstract void preprocessCreate(DTO dto);
    protected abstract VO executeCreate(DTO dto);
}
```

#### 2.1.2 角色特化控制器

```java
/**
 * 系统管理员控制器基类
 */
public abstract class BaseSystemAdminController<T, VO, DTO> 
    extends BaseCrudController<T, VO, DTO> {
    
    @Override
    protected void validateListPermission(PageQueryDTO pageQuery) {
        // 系统管理员拥有全局权限，无需额外验证
    }
    
    @Override
    protected void validateEntityPermission(VO entity) {
        // 系统管理员拥有全局权限，无需额外验证
    }
}

/**
 * 业务主体管理员控制器基类
 */
public abstract class BaseEntityAdminController<T, VO, DTO> 
    extends BaseCrudController<T, VO, DTO> {
    
    @Override
    protected void validateListPermission(PageQueryDTO pageQuery) {
        // 验证业务主体权限
        Long currentEntityId = getCurrentEntityId();
        if (pageQuery.getEntityId() != null && 
            !pageQuery.getEntityId().equals(currentEntityId)) {
            throw new BusinessException("无权访问其他业务主体的数据");
        }
    }
    
    protected abstract Long getCurrentEntityId();
}
```

### 2.2 权限控制统一化

#### 2.2.1 权限验证器模式

```java
/**
 * 权限验证器接口
 */
public interface PermissionValidator<T> {
    boolean canAccess(T target, DataPermissionContext.DataPermissionInfo permission);
    void validateAccess(T target, DataPermissionContext.DataPermissionInfo permission);
}

/**
 * 设备权限验证器
 */
@Component
public class DevicePermissionValidator implements PermissionValidator<Device> {
    
    @Override
    public boolean canAccess(Device device, DataPermissionContext.DataPermissionInfo permission) {
        switch (permission.getDataScope()) {
            case ALL:
                return true;
            case ENTITY:
                return Objects.equals(device.getEntityId(), permission.getFilterEntityId());
            case PARTNER:
                return Objects.equals(device.getPartnerId(), permission.getFilterPartnerId());
            case SHOP:
                return Objects.equals(device.getShopId(), permission.getFilterShopId());
            default:
                return false;
        }
    }
    
    @Override
    public void validateAccess(Device device, DataPermissionContext.DataPermissionInfo permission) {
        if (!canAccess(device, permission)) {
            throw new BusinessException("无权访问该设备");
        }
    }
}
```

#### 2.2.2 权限管理器

```java
/**
 * 统一权限管理器
 */
@Component
@RequiredArgsConstructor
public class PermissionManager {
    
    private final Map<Class<?>, PermissionValidator<?>> validators;
    
    @SuppressWarnings("unchecked")
    public <T> void validateAccess(T target) {
        Class<?> targetClass = target.getClass();
        PermissionValidator<T> validator = (PermissionValidator<T>) validators.get(targetClass);
        
        if (validator != null) {
            DataPermissionContext.DataPermissionInfo permission = DataPermissionContext.get();
            validator.validateAccess(target, permission);
        }
    }
    
    public <T> boolean canAccess(T target) {
        try {
            validateAccess(target);
            return true;
        } catch (BusinessException e) {
            return false;
        }
    }
}
```

### 2.3 服务层抽象设计

#### 2.3.1 通用服务接口

```java
/**
 * 通用CRUD服务接口
 */
public interface BaseCrudService<T, VO, DTO> {
    
    PageResult<VO> page(PageQueryDTO pageQuery);
    VO getById(Long id);
    VO create(DTO dto);
    VO update(Long id, DTO dto);
    void deleteById(Long id);
    void deleteBatch(List<Long> ids);
    
    // 权限相关
    PageResult<VO> getFilteredPage(PageQueryDTO pageQuery, DataPermissionContext.DataPermissionInfo permission);
    VO getFilteredById(Long id, DataPermissionContext.DataPermissionInfo permission);
}

/**
 * 设备服务接口扩展
 */
public interface DeviceService extends BaseCrudService<Device, DeviceVO, DeviceDTO> {
    
    // 设备特有方法
    DeviceVO bindDevice(Long deviceId, DeviceBindDTO bindDTO);
    void unbindDevice(Long deviceId);
    DeviceStatusVO getDeviceStatus(Long deviceId);
    void restartDevice(Long deviceId);
    
    // 离线设备处理
    List<DeviceVO> getOfflineDevices();
    void handleOfflineDevice(Long deviceId, OfflineHandleDTO handleDTO);
}
```

#### 2.3.2 抽象服务实现

```java
/**
 * 通用CRUD服务抽象实现
 */
@Slf4j
public abstract class BaseCrudServiceImpl<T, VO, DTO, M extends BaseMapper<T>> 
    implements BaseCrudService<T, VO, DTO> {
    
    @Autowired
    protected M baseMapper;
    
    @Override
    public PageResult<VO> page(PageQueryDTO pageQuery) {
        DataPermissionContext.DataPermissionInfo permission = DataPermissionContext.get();
        return getFilteredPage(pageQuery, permission);
    }
    
    @Override
    public VO getById(Long id) {
        DataPermissionContext.DataPermissionInfo permission = DataPermissionContext.get();
        return getFilteredById(id, permission);
    }
    
    // 抽象方法
    protected abstract VO convertToVO(T entity);
    protected abstract T convertToEntity(DTO dto);
    protected abstract void validateCreate(DTO dto);
    protected abstract void validateUpdate(Long id, DTO dto);
}
```

### 2.4 离线设备处理方案

#### 2.4.1 设备状态管理器

```java
/**
 * 设备状态管理器
 */
@Component
@RequiredArgsConstructor
public class DeviceStatusManager {
    
    private final DeviceService deviceService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 检查设备是否在线
     */
    public boolean isDeviceOnline(Long deviceId) {
        String key = "device:status:" + deviceId;
        return redisTemplate.hasKey(key);
    }
    
    /**
     * 设置设备在线状态
     */
    public void setDeviceOnline(Long deviceId) {
        String key = "device:status:" + deviceId;
        redisTemplate.opsForValue().set(key, "online", Duration.ofMinutes(5));
    }
    
    /**
     * 处理设备离线
     */
    public void handleDeviceOffline(Long deviceId) {
        Device device = deviceService.getById(deviceId);
        if (device != null) {
            // 更新设备状态
            device.setOnlineStatus(0);
            device.setLastOfflineTime(LocalDateTime.now());
            deviceService.updateById(device);
            
            // 发送离线通知
            publishDeviceOfflineEvent(device);
        }
    }
    
    /**
     * 执行需要设备在线的操作
     */
    public <T> T executeOnlineOperation(Long deviceId, Supplier<T> operation) {
        if (!isDeviceOnline(deviceId)) {
            throw new DeviceOfflineException("设备离线，无法执行操作");
        }
        return operation.get();
    }
}
```

#### 2.4.2 离线操作队列

```java
/**
 * 离线操作队列管理
 */
@Component
public class OfflineOperationQueue {
    
    private final Map<Long, Queue<DeviceOperation>> operationQueues = new ConcurrentHashMap<>();
    
    /**
     * 添加离线操作到队列
     */
    public void addOperation(Long deviceId, DeviceOperation operation) {
        operationQueues.computeIfAbsent(deviceId, k -> new ConcurrentLinkedQueue<>())
                      .offer(operation);
    }
    
    /**
     * 设备上线时执行队列中的操作
     */
    public void executeQueuedOperations(Long deviceId) {
        Queue<DeviceOperation> queue = operationQueues.get(deviceId);
        if (queue != null) {
            while (!queue.isEmpty()) {
                DeviceOperation operation = queue.poll();
                try {
                    operation.execute();
                } catch (Exception e) {
                    log.error("执行离线操作失败", e);
                }
            }
        }
    }
}
```

## 3. 具体实施步骤

### 阶段一：基础架构重构（2-3周）

1. **创建通用基础控制器**
   - 实现BaseCrudController
   - 创建角色特化控制器基类
   - 迁移现有控制器到新架构

2. **统一权限控制**
   - 实现PermissionValidator接口
   - 创建各模块的权限验证器
   - 重构权限管理逻辑

3. **服务层抽象**
   - 定义通用服务接口
   - 实现抽象服务基类
   - 重构现有服务实现

### 阶段二：模块迁移（3-4周）

1. **优先迁移核心模块**
   - 设备管理模块
   - 财务管理模块
   - 合作商管理模块

2. **逐步迁移其他模块**
   - 门店管理模块
   - 订单管理模块
   - 用户管理模块

### 阶段三：离线设备处理优化（1-2周）

1. **实现设备状态管理器**
2. **创建离线操作队列**
3. **优化设备监控机制**

### 阶段四：测试和优化（1-2周）

1. **全面测试重构后的功能**
2. **性能优化**
3. **文档更新**

## 4. 风险评估与应对

### 4.1 技术风险

**风险**：重构过程中可能引入新的Bug
**应对**：
- 采用渐进式重构，保持向后兼容
- 完善的单元测试和集成测试
- 灰度发布策略

**风险**：性能可能受到影响
**应对**：
- 性能基准测试
- 关键路径的性能监控
- 必要时进行性能优化

### 4.2 业务风险

**风险**：重构期间可能影响业务功能
**应对**：
- 分模块逐步重构
- 保持API兼容性
- 充分的回归测试

### 4.3 时间风险

**风险**：重构时间可能超出预期
**应对**：
- 详细的任务分解和时间估算
- 定期进度检查和调整
- 必要时调整重构范围

## 5. 预期收益

### 5.1 代码质量提升

- **减少重复代码**：预计减少40-50%的重复代码
- **提高可维护性**：统一的架构模式便于维护
- **增强可扩展性**：新功能开发效率提升30%

### 5.2 开发效率提升

- **新功能开发**：基于统一架构，开发效率提升
- **Bug修复**：统一的错误处理机制，问题定位更快
- **代码审查**：标准化的代码结构，审查效率提升

### 5.3 系统稳定性提升

- **权限控制**：统一的权限管理，安全性提升
- **错误处理**：标准化的异常处理机制
- **监控能力**：统一的日志和监控体系

## 6. 具体代码实现示例

### 6.1 重构后的设备控制器示例

```java
/**
 * 系统管理员设备控制器
 */
@RestController
@RequestMapping("/admin/device")
@Tag(name = "系统管理员-设备管理")
public class AdminDeviceController extends BaseSystemAdminController<Device, DeviceVO, DeviceDTO> {

    @Autowired
    private DeviceService deviceService;

    @Override
    protected DeviceService getService() {
        return deviceService;
    }

    // 设备特有操作
    @PostMapping("/{id}/restart")
    @Operation(summary = "重启设备")
    public CommonResult<Void> restartDevice(@PathVariable Long id) {
        deviceService.restartDevice(id);
        return CommonResult.success();
    }
}

/**
 * 业务主体管理员设备控制器
 */
@RestController
@RequestMapping("/entity/device")
@Tag(name = "业务主体管理员-设备管理")
public class EntityDeviceController extends BaseEntityAdminController<Device, DeviceVO, DeviceDTO> {

    @Autowired
    private DeviceService deviceService;

    @Override
    protected DeviceService getService() {
        return deviceService;
    }

    // 只能查看，不能创建/删除设备
    @Override
    protected void validateCreatePermission(DeviceDTO dto) {
        throw new BusinessException("业务主体管理员无权创建设备");
    }

    @Override
    protected void validateDeletePermission(Long id) {
        throw new BusinessException("业务主体管理员无权删除设备");
    }
}
```

### 6.2 统一的异常处理

```java
/**
 * 设备离线异常
 */
public class DeviceOfflineException extends BusinessException {
    public DeviceOfflineException(String message) {
        super(ErrorCode.DEVICE_OFFLINE, message);
    }
}

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(DeviceOfflineException.class)
    public CommonResult<Void> handleDeviceOfflineException(DeviceOfflineException e) {
        return CommonResult.failed(e.getErrorCode(), e.getMessage());
    }
}
```

### 6.3 设备操作装饰器模式

```java
/**
 * 设备操作装饰器
 */
@Component
public class DeviceOperationDecorator {

    @Autowired
    private DeviceStatusManager deviceStatusManager;

    @Autowired
    private OfflineOperationQueue offlineOperationQueue;

    /**
     * 装饰设备操作，支持离线队列
     */
    public <T> T decorateOperation(Long deviceId, String operationName,
                                  Supplier<T> operation, boolean allowOffline) {
        if (deviceStatusManager.isDeviceOnline(deviceId)) {
            return operation.get();
        } else if (allowOffline) {
            // 添加到离线操作队列
            DeviceOperation deviceOperation = new DeviceOperation(deviceId, operationName, operation);
            offlineOperationQueue.addOperation(deviceId, deviceOperation);
            return null;
        } else {
            throw new DeviceOfflineException("设备离线，无法执行操作：" + operationName);
        }
    }
}
```

## 7. 配置和监控

### 7.1 配置管理

```yaml
# application.yml
jycb:
  architecture:
    # 权限控制配置
    permission:
      strict-mode: true  # 严格模式
      cache-enabled: true  # 权限缓存

    # 设备管理配置
    device:
      offline-timeout: 300  # 离线超时时间（秒）
      max-offline-operations: 100  # 最大离线操作数
      heartbeat-interval: 30  # 心跳间隔（秒）

    # 性能监控配置
    monitoring:
      enabled: true
      slow-query-threshold: 1000  # 慢查询阈值（毫秒）
```

### 7.2 监控指标

```java
/**
 * 架构监控指标
 */
@Component
public class ArchitectureMetrics {

    private final MeterRegistry meterRegistry;

    // 权限验证次数
    private final Counter permissionCheckCounter;

    // 设备离线操作次数
    private final Counter offlineOperationCounter;

    // 慢查询次数
    private final Counter slowQueryCounter;

    public ArchitectureMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.permissionCheckCounter = Counter.builder("permission.check.count")
            .description("权限验证次数")
            .register(meterRegistry);
        // ... 其他指标初始化
    }
}
```

这个优化方案将显著提升今夜城堡项目的架构质量和开发效率，为后续的功能扩展和维护奠定坚实基础。
