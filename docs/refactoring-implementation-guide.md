# 今夜城堡项目重构实施指南

## 1. 重构前准备工作

### 1.1 代码备份和分支管理

```bash
# 创建重构分支
git checkout -b feature/architecture-refactoring

# 创建各阶段分支
git checkout -b phase1/base-controller-refactoring
git checkout -b phase2/permission-unification
git checkout -b phase3/service-layer-abstraction
git checkout -b phase4/offline-device-handling
```

### 1.2 测试环境准备

1. **建立完整的测试数据集**
2. **配置自动化测试环境**
3. **设置性能基准测试**
4. **准备回滚方案**

### 1.3 依赖分析

分析现有模块间的依赖关系，确定重构顺序：

```
优先级1: common模块（基础设施）
优先级2: device模块（已有部分基础）
优先级3: finance模块（已有部分基础）
优先级4: partner模块
优先级5: shop模块
优先级6: order模块
```

## 2. 阶段一：基础控制器重构

### 2.1 创建通用基础控制器

**文件路径**: `src/main/java/com/jycb/jycbz/common/controller/BaseCrudController.java`

```java
package com.jycb.jycbz.common.controller;

import com.jycb.jycbz.common.annotation.DataPermission;
import com.jycb.jycbz.common.api.CommonResult;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.dto.PageQueryDTO;
import com.jycb.jycbz.common.service.BaseCrudService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 通用CRUD基础控制器
 * 提供标准的CRUD操作，子类可以根据角色权限进行定制
 */
@Slf4j
public abstract class BaseCrudController<T, VO, DTO> {

    /**
     * 获取服务实例，子类必须实现
     */
    protected abstract BaseCrudService<T, VO, DTO> getService();

    /**
     * 分页查询
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<PageResult<VO>> page(@Valid PageQueryDTO pageQuery) {
        // 前置权限验证
        validateListPermission(pageQuery);
        
        // 执行查询
        PageResult<VO> result = getService().page(pageQuery);
        
        // 后置处理
        postProcessPageResult(result);
        
        return CommonResult.success(result);
    }

    /**
     * 根据ID查询详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询详情")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<VO> getById(@PathVariable Long id) {
        VO result = getService().getById(id);
        
        // 权限验证
        validateEntityPermission(result);
        
        return CommonResult.success(result);
    }

    /**
     * 创建
     */
    @PostMapping
    @Operation(summary = "创建")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<VO> create(@Valid @RequestBody DTO dto) {
        // 权限验证
        validateCreatePermission(dto);
        
        // 预处理
        preprocessCreate(dto);
        
        // 执行创建
        VO result = getService().create(dto);
        
        // 后处理
        postProcessCreate(result);
        
        return CommonResult.success(result);
    }

    /**
     * 更新
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<VO> update(@PathVariable Long id, @Valid @RequestBody DTO dto) {
        // 权限验证
        validateUpdatePermission(id, dto);
        
        // 预处理
        preprocessUpdate(id, dto);
        
        // 执行更新
        VO result = getService().update(id, dto);
        
        // 后处理
        postProcessUpdate(result);
        
        return CommonResult.success(result);
    }

    /**
     * 删除
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Void> deleteById(@PathVariable Long id) {
        // 权限验证
        validateDeletePermission(id);
        
        // 预处理
        preprocessDelete(id);
        
        // 执行删除
        getService().deleteById(id);
        
        // 后处理
        postProcessDelete(id);
        
        return CommonResult.success();
    }

    /**
     * 批量删除
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Void> deleteBatch(@RequestBody List<Long> ids) {
        // 权限验证
        validateBatchDeletePermission(ids);
        
        // 执行批量删除
        getService().deleteBatch(ids);
        
        return CommonResult.success();
    }

    // ==================== 钩子方法，子类可以重写 ====================

    /**
     * 验证列表查询权限
     */
    protected void validateListPermission(PageQueryDTO pageQuery) {
        // 默认实现，子类可重写
    }

    /**
     * 验证实体访问权限
     */
    protected void validateEntityPermission(VO entity) {
        // 默认实现，子类可重写
    }

    /**
     * 验证创建权限
     */
    protected void validateCreatePermission(DTO dto) {
        // 默认实现，子类可重写
    }

    /**
     * 验证更新权限
     */
    protected void validateUpdatePermission(Long id, DTO dto) {
        // 默认实现，子类可重写
    }

    /**
     * 验证删除权限
     */
    protected void validateDeletePermission(Long id) {
        // 默认实现，子类可重写
    }

    /**
     * 验证批量删除权限
     */
    protected void validateBatchDeletePermission(List<Long> ids) {
        // 默认实现，子类可重写
        ids.forEach(this::validateDeletePermission);
    }

    /**
     * 创建前预处理
     */
    protected void preprocessCreate(DTO dto) {
        // 默认实现，子类可重写
    }

    /**
     * 更新前预处理
     */
    protected void preprocessUpdate(Long id, DTO dto) {
        // 默认实现，子类可重写
    }

    /**
     * 删除前预处理
     */
    protected void preprocessDelete(Long id) {
        // 默认实现，子类可重写
    }

    /**
     * 分页结果后处理
     */
    protected void postProcessPageResult(PageResult<VO> result) {
        // 默认实现，子类可重写
    }

    /**
     * 创建后处理
     */
    protected void postProcessCreate(VO result) {
        // 默认实现，子类可重写
    }

    /**
     * 更新后处理
     */
    protected void postProcessUpdate(VO result) {
        // 默认实现，子类可重写
    }

    /**
     * 删除后处理
     */
    protected void postProcessDelete(Long id) {
        // 默认实现，子类可重写
    }
}
```

### 2.2 创建角色特化控制器

**文件路径**: `src/main/java/com/jycb/jycbz/common/controller/BaseSystemAdminController.java`

```java
package com.jycb.jycbz.common.controller;

import com.jycb.jycbz.common.dto.PageQueryDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * 系统管理员控制器基类
 * 系统管理员拥有全局权限，无需额外的权限验证
 */
@Slf4j
public abstract class BaseSystemAdminController<T, VO, DTO> 
    extends BaseCrudController<T, VO, DTO> {

    @Override
    protected void validateListPermission(PageQueryDTO pageQuery) {
        // 系统管理员拥有全局权限，无需额外验证
        log.debug("系统管理员访问，跳过权限验证");
    }

    @Override
    protected void validateEntityPermission(VO entity) {
        // 系统管理员拥有全局权限，无需额外验证
        log.debug("系统管理员访问，跳过权限验证");
    }
}
```

### 2.3 重构现有控制器

以设备控制器为例，展示重构过程：

**重构前**: `AdminDeviceController.java`
```java
// 原有的大量重复代码...
```

**重构后**: `AdminDeviceController.java`
```java
@RestController
@RequestMapping("/admin/device")
@Tag(name = "系统管理员-设备管理")
public class AdminDeviceController extends BaseSystemAdminController<Device, DeviceVO, DeviceDTO> {
    
    @Autowired
    private DeviceService deviceService;
    
    @Override
    protected DeviceService getService() {
        return deviceService;
    }
    
    // 只需要实现设备特有的操作
    @PostMapping("/{id}/restart")
    @Operation(summary = "重启设备")
    public CommonResult<Void> restartDevice(@PathVariable Long id) {
        deviceService.restartDevice(id);
        return CommonResult.success();
    }
    
    @PostMapping("/{id}/bind")
    @Operation(summary = "绑定设备")
    public CommonResult<Void> bindDevice(@PathVariable Long id, @RequestBody DeviceBindDTO bindDTO) {
        deviceService.bindDevice(id, bindDTO);
        return CommonResult.success();
    }
}
```

## 3. 阶段二：权限控制统一化

### 3.1 创建权限验证器

**文件路径**: `src/main/java/com/jycb/jycbz/common/permission/PermissionValidator.java`

```java
package com.jycb.jycbz.common.permission;

import com.jycb.jycbz.common.context.DataPermissionContext;

/**
 * 权限验证器接口
 */
public interface PermissionValidator<T> {
    
    /**
     * 检查是否可以访问
     */
    boolean canAccess(T target, DataPermissionContext.DataPermissionInfo permission);
    
    /**
     * 验证访问权限，失败时抛出异常
     */
    void validateAccess(T target, DataPermissionContext.DataPermissionInfo permission);
    
    /**
     * 获取支持的实体类型
     */
    Class<T> getSupportedType();
}
```

### 3.2 实现具体的权限验证器

**文件路径**: `src/main/java/com/jycb/jycbz/modules/device/permission/DevicePermissionValidator.java`

```java
@Component
public class DevicePermissionValidator implements PermissionValidator<Device> {
    
    @Override
    public boolean canAccess(Device device, DataPermissionContext.DataPermissionInfo permission) {
        if (permission == null) {
            return false;
        }
        
        switch (permission.getDataScope()) {
            case ALL:
                return true;
            case ENTITY:
                return Objects.equals(device.getEntityId(), permission.getFilterEntityId());
            case PARTNER:
                return Objects.equals(device.getPartnerId(), permission.getFilterPartnerId());
            case SHOP:
                return Objects.equals(device.getShopId(), permission.getFilterShopId());
            default:
                return false;
        }
    }
    
    @Override
    public void validateAccess(Device device, DataPermissionContext.DataPermissionInfo permission) {
        if (!canAccess(device, permission)) {
            throw new BusinessException(ErrorCode.ACCESS_DENIED, "无权访问该设备");
        }
    }
    
    @Override
    public Class<Device> getSupportedType() {
        return Device.class;
    }
}
```

## 4. 阶段三：服务层抽象

### 4.1 创建通用服务接口

**文件路径**: `src/main/java/com/jycb/jycbz/common/service/BaseCrudService.java`

```java
package com.jycb.jycbz.common.service;

import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.dto.PageQueryDTO;

import java.util.List;

/**
 * 通用CRUD服务接口
 */
public interface BaseCrudService<T, VO, DTO> {
    
    /**
     * 分页查询
     */
    PageResult<VO> page(PageQueryDTO pageQuery);
    
    /**
     * 根据ID查询
     */
    VO getById(Long id);
    
    /**
     * 创建
     */
    VO create(DTO dto);
    
    /**
     * 更新
     */
    VO update(Long id, DTO dto);
    
    /**
     * 删除
     */
    void deleteById(Long id);
    
    /**
     * 批量删除
     */
    void deleteBatch(List<Long> ids);
}
```

## 5. 重构验证和测试

### 5.1 单元测试

为每个重构的组件编写单元测试：

```java
@ExtendWith(MockitoExtension.class)
class BaseCrudControllerTest {
    
    @Mock
    private DeviceService deviceService;
    
    @InjectMocks
    private AdminDeviceController controller;
    
    @Test
    void testPageQuery() {
        // 测试分页查询功能
    }
    
    @Test
    void testPermissionValidation() {
        // 测试权限验证功能
    }
}
```

### 5.2 集成测试

```java
@SpringBootTest
@AutoConfigureTestDatabase
class DeviceControllerIntegrationTest {
    
    @Test
    void testFullCrudWorkflow() {
        // 测试完整的CRUD流程
    }
}
```

### 5.3 性能测试

```java
@Test
void testPerformance() {
    // 对比重构前后的性能
    StopWatch stopWatch = new StopWatch();
    stopWatch.start();
    
    // 执行操作
    
    stopWatch.stop();
    assertThat(stopWatch.getTotalTimeMillis()).isLessThan(1000);
}
```

## 6. 部署和监控

### 6.1 灰度发布策略

1. **内测环境验证**
2. **小范围用户测试**
3. **逐步扩大范围**
4. **全量发布**

### 6.2 监控指标

- API响应时间
- 错误率
- 权限验证成功率
- 设备操作成功率

### 6.3 回滚方案

准备快速回滚机制，确保在出现问题时能够快速恢复。

这个实施指南提供了详细的重构步骤和代码示例，确保重构过程的顺利进行。
