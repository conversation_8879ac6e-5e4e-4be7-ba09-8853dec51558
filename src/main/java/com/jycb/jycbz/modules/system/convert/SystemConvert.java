package com.jycb.jycbz.modules.system.convert;

import ch.qos.logback.core.status.Status;
import com.jycb.jycbz.modules.system.dto.MenuCreateDTO;
import com.jycb.jycbz.modules.system.dto.MenuUpdateDTO;
import com.jycb.jycbz.modules.system.entity.Menu;
import com.jycb.jycbz.modules.system.vo.MenuVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 系统模块转换器
 */
@Mapper(componentModel = "spring")
public interface SystemConvert {

    SystemConvert INSTANCE = Mappers.getMapper(SystemConvert.class);

    // ==================== 菜单转换 ====================

    /**
     * 菜单创建DTO转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    Menu toEntity(MenuCreateDTO createDTO);

    /**
     * 菜单更新DTO转实体
     */
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    Menu toEntity(MenuUpdateDTO updateDTO);

    /**
     * 菜单实体转VO
     */
    @Mapping(target = "statusName", expression = "java(getStatusName(menu.getStatus()))")
    @Mapping(target = "menuTypeName", source = "menuType", qualifiedByName = "menuTypeToName")
    @Mapping(target = "parentName", ignore = true) // 需要额外查询父菜单名称
    @Mapping(target = "children", ignore = true) // 树形结构子节点
    @Mapping(target = "hasChildren", ignore = true) // 是否有子节点
    @Mapping(target = "level", ignore = true) // 菜单层级
    @Mapping(target = "treePath", ignore = true) // 树形路径
    @Mapping(target = "meta", ignore = true) // 元数据信息
    MenuVO toVO(Menu menu);

    /**
     * 菜单实体列表转VO列表
     */
    List<MenuVO> toVOList(List<Menu> menuList);

    /**
     * 更新菜单实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    void updateEntity(MenuUpdateDTO updateDTO, @MappingTarget Menu menu);




    // ==================== 辅助方法 ====================

    /**
     * 获取状态名称
     */
    default String getStatusName(Integer status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case 0:
                return "禁用";
            case 1:
                return "启用";
            default:
                return "未知";
        }
    }

    /**
     * 获取菜单类型名称
     */
    @Named("menuTypeToName")
    default String getMenuTypeName(Integer type) {
        if (type == null) {
            return null;
        }
        switch (type) {
            case 0:
                return "目录";
            case 1:
                return "菜单";
            case 2:
                return "按钮";
            default:
                return "未知";
        }
    }


}
