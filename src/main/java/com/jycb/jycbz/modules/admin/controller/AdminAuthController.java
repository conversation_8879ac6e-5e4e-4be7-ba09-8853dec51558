package com.jycb.jycbz.modules.admin.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import com.jycb.jycbz.common.annotation.Anonymous;
import com.jycb.jycbz.common.annotation.Auditable;
import com.jycb.jycbz.common.annotation.DataPermission;
import com.jycb.jycbz.common.api.CommonResult;
import com.jycb.jycbz.common.constant.AuditConstants;
import com.jycb.jycbz.modules.auth.dto.AdminLoginDTO;
import com.jycb.jycbz.modules.admin.entity.Admin;
import com.jycb.jycbz.modules.admin.service.AdminService;
import com.jycb.jycbz.modules.admin.vo.AdminVO;
import com.jycb.jycbz.modules.auth.service.AuthService;
import com.jycb.jycbz.modules.auth.vo.LoginResultVO;
import com.jycb.jycbz.modules.system.service.MenuService;
import com.jycb.jycbz.modules.system.service.PermissionService;
import com.jycb.jycbz.modules.system.vo.MenuVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员认证控制器
 * 基于Sa-Token实现登录认证
 */
@Slf4j
@RestController
@RequestMapping("/admin/auth")
@RequiredArgsConstructor
@Validated
@Tag(name = "管理员认证", description = "管理员登录、登出、权限验证接口")
public class AdminAuthController {

    private final AdminService adminService;
    private final AuthService authService;
    private final MenuService menuService;
    private final PermissionService permissionService;

    /**
     * 管理员登录
     */
    @Operation(summary = "管理员登录")
    @PostMapping("/login")
    @Auditable(
        module = AuditConstants.Module.ADMIN,
        operation = AuditConstants.Operation.LOGIN,
        description = "管理员登录",
        targetType = AuditConstants.TargetType.ADMIN
    )
    @Anonymous
    public CommonResult<Map<String, Object>> login(
            @Valid @RequestBody AdminLoginDTO loginDTO,
            HttpServletRequest request) {

        log.info("管理员登录，用户名: {}", loginDTO.getUsername());

        try {
            // 使用统一的AuthService进行登录
            LoginResultVO loginResult = authService.adminLogin(loginDTO);

            // 获取用户权限和菜单
            log.debug("开始获取管理员权限，ID: {}", loginResult.getUserId());
            List<String> permissions = permissionService.getPermissionsByAdminId(loginResult.getUserId());
            log.debug("获取管理员权限成功，权限数量: {}", permissions.size());

            log.debug("开始获取管理员菜单，ID: {}", loginResult.getUserId());
            List<MenuVO> menus = menuService.getMenusByAdminId(loginResult.getUserId());
            log.debug("获取管理员菜单成功，菜单数量: {}", menus.size());

            // 构建返回数据，保持与原来格式兼容
            Map<String, Object> result = new HashMap<>();
            result.put("token", loginResult.getToken());
            result.put("tokenName", "Authorization");
            result.put("tokenTimeout", loginResult.getExpiresIn());

            // 构建admin信息
            Map<String, Object> adminInfo = new HashMap<>();
            adminInfo.put("id", loginResult.getUserId());
            adminInfo.put("username", loginResult.getUsername());
            adminInfo.put("realName", loginResult.getRealName());
            adminInfo.put("avatar", loginResult.getAvatar());
            adminInfo.put("adminType", loginResult.getAdminType());
            adminInfo.put("entityId", loginResult.getEntityId());
            adminInfo.put("partnerId", loginResult.getPartnerId());
            adminInfo.put("shopId", loginResult.getShopId());

            result.put("admin", adminInfo);
            result.put("permissions", permissions);
            result.put("menus", menus);

            log.info("管理员登录成功，用户名: {}, ID: {}", loginDTO.getUsername(), loginResult.getUserId());

            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("管理员登录失败，用户名: {}, 错误: {}", loginDTO.getUsername(), e.getMessage());
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 管理员登出
     */
    @Operation(summary = "管理员登出")
    @PostMapping("/logout")
    @SaCheckLogin
    @Auditable(
        module = AuditConstants.Module.ADMIN,
        operation = AuditConstants.Operation.LOGOUT,
        description = "管理员登出",
        targetType = AuditConstants.TargetType.ADMIN
    )
    public CommonResult<String> logout() {
        String loginId = StpUtil.getLoginIdAsString();
        log.info("管理员登出，ID: {}", loginId);
        
        StpUtil.logout();
        
        return CommonResult.success("登出成功");
    }

    /**
     * 获取当前登录管理员信息
     */
    @Operation(summary = "获取当前登录管理员信息")
    @GetMapping("/info")
    @SaCheckLogin
    @Auditable(
        module = AuditConstants.Module.ADMIN,
        operation = AuditConstants.Operation.READ,
        description = "获取当前登录管理员信息",
        targetType = AuditConstants.TargetType.ADMIN
    )
    public CommonResult<Map<String, Object>> getInfo() {
        Admin currentAdmin = adminService.getCurrentAdmin();
        
        if (currentAdmin == null) {
            return CommonResult.failed("获取用户信息失败");
        }
        
        // 获取用户权限和菜单
        List<String> permissions = permissionService.getPermissionsByAdminId(currentAdmin.getId());
        List<MenuVO> menus = menuService.getMenusByAdminId(currentAdmin.getId());
        
        Map<String, Object> result = new HashMap<>();
        result.put("admin", convertToVO(currentAdmin));
        result.put("permissions", permissions);
        result.put("menus", menus);
        
        return CommonResult.success(result);
    }

    /**
     * 刷新Token
     */
    @Operation(summary = "刷新Token")
    @PostMapping("/refresh")
    @SaCheckLogin
    public CommonResult<Map<String, Object>> refresh() {
        // 续签Token
        StpUtil.renewTimeout(7200); // 续签2小时
        
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        
        Map<String, Object> result = new HashMap<>();
        result.put("token", tokenInfo.getTokenValue());
        result.put("tokenName", tokenInfo.getTokenName());
        result.put("tokenTimeout", tokenInfo.getTokenTimeout());
        
        return CommonResult.success(result);
    }

    /**
     * 检查登录状态
     */
    @Operation(summary = "检查登录状态")
    @GetMapping("/check")
    public CommonResult<Map<String, Object>> checkLogin() {
        boolean isLogin = StpUtil.isLogin();
        
        Map<String, Object> result = new HashMap<>();
        result.put("isLogin", isLogin);
        
        if (isLogin) {
            result.put("loginId", StpUtil.getLoginIdAsString());
            result.put("tokenTimeout", StpUtil.getTokenTimeout());
        }
        
        return CommonResult.success(result);
    }

    /**
     * 获取在线用户列表
     */
    @Operation(summary = "获取在线用户列表")
    @GetMapping("/online")
    @SaCheckLogin
    @SaCheckPermission("admin:auth:online")
    @DataPermission(type = DataPermission.PermissionType.AUTO, description = "获取在线用户列表数据权限控制")
    public CommonResult<List<Map<String, Object>>> getOnlineUsers() {
        try {
            List<Map<String, Object>> onlineUsers = new ArrayList<>();

            // 方法1：使用Sa-Token的会话搜索
            List<String> sessionIdList = StpUtil.searchSessionId("", 0, -1, false);
            log.debug("搜索到的session数量: {}", sessionIdList.size());

            for (String sessionId : sessionIdList) {
                try {
                    log.debug("处理sessionId: {}", sessionId);
                    // 从sessionId中提取loginId
                    if (sessionId.startsWith("Authorization:login:session:")) {
                        String loginIdStr = sessionId.replace("Authorization:login:session:", "");
                        log.debug("提取的loginId: {}", loginIdStr);

                        try {
                            Long loginId = Long.valueOf(loginIdStr);
                            // 检查用户是否真的在线
                            if (StpUtil.isLogin(loginId)) {
                                // 获取用户信息
                                Admin admin = adminService.getById(loginId);
                                log.debug("查询到的admin: {}", admin != null ? admin.getUsername() : "null");
                                if (admin != null) {
                                    Map<String, Object> userInfo = new HashMap<>();
                                    userInfo.put("adminId", admin.getId());
                                    userInfo.put("username", admin.getUsername());
                                    userInfo.put("realName", admin.getRealName());
                                    userInfo.put("adminType", admin.getAdminType());
                                    userInfo.put("mobile", admin.getMobile());
                                    userInfo.put("email", admin.getEmail());
                                    userInfo.put("loginTime", admin.getLoginTime());
                                    userInfo.put("loginIp", admin.getLoginIp());

                                    // 获取 token 相关信息
                                    try {
                                        String currentToken = StpUtil.getTokenValueByLoginId(loginId);
                                        userInfo.put("tokenValue", currentToken);
                                        userInfo.put("loginDevice", "web"); // 默认设备类型

                                        // 切换到指定用户的上下文来获取token信息
                                        Object originalLoginId = null;
                                        boolean needRestore = false;
                                        if (StpUtil.isLogin() && !StpUtil.getLoginId().equals(loginId)) {
                                            originalLoginId = StpUtil.getLoginId();
                                            needRestore = true;
                                        }

                                        // 获取token超时信息
                                        userInfo.put("tokenTimeout", StpUtil.getTokenTimeout());
                                        userInfo.put("sessionTimeout", StpUtil.getSessionTimeout());

                                    } catch (Exception e) {
                                        // 忽略token信息获取失败
                                        log.debug("获取token信息失败: {}", e.getMessage());
                                    }

                                    onlineUsers.add(userInfo);
                                }
                            }
                        } catch (NumberFormatException e) {
                            log.debug("无效的loginId格式: {}", loginIdStr);
                        }
                    }
                } catch (Exception e) {
                    // 忽略无效的 session，继续处理下一个
                    log.warn("处理在线用户session时出错: {}", e.getMessage());
                }
            }

            log.info("获取在线用户列表成功，共{}个用户在线", onlineUsers.size());
            return CommonResult.success(onlineUsers);

        } catch (Exception e) {
            log.error("获取在线用户列表失败", e);
            return CommonResult.failed("获取在线用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取在线用户列表（分页版本）
     */
    @Operation(summary = "获取在线用户列表（分页）")
    @GetMapping("/online/page")
    @SaCheckLogin
    @SaCheckPermission("admin:auth:online")
    @DataPermission(type = DataPermission.PermissionType.AUTO, description = "获取在线用户分页列表数据权限控制")
    public CommonResult<Map<String, Object>> getOnlineUsersPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String keyword) {
        try {
            List<Map<String, Object>> allOnlineUsers = new ArrayList<>();

            // 获取所有在线的 token 列表
            List<String> tokenList = StpUtil.searchTokenValue("", 0, -1, false);

            for (String token : tokenList) {
                try {
                    Object loginId = StpUtil.getLoginIdByToken(token);
                    if (loginId != null) {
                        Admin admin = adminService.getById(Long.valueOf(loginId.toString()));
                        if (admin != null) {
                            // 如果有关键字搜索，进行过滤
                            if (keyword != null && !keyword.trim().isEmpty()) {
                                String lowerKeyword = keyword.toLowerCase();
                                if (!admin.getUsername().toLowerCase().contains(lowerKeyword) &&
                                    (admin.getRealName() == null || !admin.getRealName().toLowerCase().contains(lowerKeyword)) &&
                                    (admin.getMobile() == null || !admin.getMobile().contains(keyword))) {
                                    continue;
                                }
                            }

                            Map<String, Object> userInfo = new HashMap<>();
                            userInfo.put("adminId", admin.getId());
                            userInfo.put("username", admin.getUsername());
                            userInfo.put("realName", admin.getRealName());
                            userInfo.put("adminType", admin.getAdminType());
                            userInfo.put("mobile", admin.getMobile());
                            userInfo.put("email", admin.getEmail());
                            userInfo.put("loginTime", admin.getLoginTime());
                            userInfo.put("loginIp", admin.getLoginIp());
                            userInfo.put("status", admin.getStatus());

                            // 获取 token 相关信息
                            try {
                                userInfo.put("tokenValue", token);
                                userInfo.put("loginDevice", "web"); // 默认设备类型
                                userInfo.put("tokenTimeout", StpUtil.getTokenTimeout());
                                userInfo.put("sessionTimeout", StpUtil.getSessionTimeout());
                                userInfo.put("tokenActiveTimeout", StpUtil.getTokenActiveTimeout());
                            } catch (Exception e) {
                                // 忽略token信息获取失败
                                log.debug("获取token信息失败: {}", e.getMessage());
                            }

                            allOnlineUsers.add(userInfo);
                        }
                    }
                } catch (Exception e) {
                    log.warn("处理在线用户token时出错: {}", e.getMessage());
                }
            }

            // 手动分页
            int total = allOnlineUsers.size();
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<Map<String, Object>> pagedUsers = new ArrayList<>();
            if (startIndex < total) {
                pagedUsers = allOnlineUsers.subList(startIndex, endIndex);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("list", pagedUsers);
            result.put("total", total);
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("pages", (total + pageSize - 1) / pageSize);

            log.info("获取在线用户分页列表成功，第{}页，共{}个用户在线", pageNum, total);
            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("获取在线用户分页列表失败", e);
            return CommonResult.failed("获取在线用户分页列表失败: " + e.getMessage());
        }
    }

    /**
     * 强制下线用户
     */
    @Operation(summary = "强制下线用户")
    @PostMapping("/kickout/{adminId}")
    @SaCheckLogin
    @SaCheckPermission("admin:auth:kickout")
    @DataPermission(type = DataPermission.PermissionType.AUTO, description = "强制下线用户数据权限控制")
    @Auditable(
        module = AuditConstants.Module.ADMIN,
        operation = AuditConstants.Operation.UPDATE,
        description = "强制下线用户",
        targetType = AuditConstants.TargetType.ADMIN,
        targetId = "#adminId"
    )
    public CommonResult<String> kickout(@PathVariable Long adminId) {
        try {
            // 获取当前操作用户
            Long currentAdminId = StpUtil.getLoginIdAsLong();

            // 不能强制下线自己
            if (currentAdminId.equals(adminId)) {
                return CommonResult.failed("不能强制下线自己");
            }

            // 检查目标用户是否存在
            Admin targetAdmin = adminService.getById(adminId);
            if (targetAdmin == null) {
                return CommonResult.failed("目标用户不存在");
            }

            // 检查目标用户是否在线
            if (!StpUtil.isLogin(adminId)) {
                return CommonResult.failed("目标用户当前不在线");
            }

            log.info("强制下线用户，操作者ID: {}, 目标用户ID: {}, 目标用户名: {}",
                    currentAdminId, adminId, targetAdmin.getUsername());

            // 执行强制下线
            StpUtil.kickout(adminId);

            return CommonResult.success("用户 " + targetAdmin.getUsername() + " 已强制下线");

        } catch (Exception e) {
            log.error("强制下线用户失败，目标用户ID: {}", adminId, e);
            return CommonResult.failed("强制下线失败: " + e.getMessage());
        }
    }

    /**
     * 批量强制下线用户
     */
    @Operation(summary = "批量强制下线用户")
    @PostMapping("/kickout/batch")
    @SaCheckLogin
    @SaCheckPermission("admin:auth:kickout")
    @DataPermission(type = DataPermission.PermissionType.AUTO, description = "批量强制下线用户数据权限控制")
    @Auditable(
        module = AuditConstants.Module.ADMIN,
        operation = AuditConstants.Operation.UPDATE,
        description = "批量强制下线用户"
    )
    public CommonResult<Map<String, Object>> batchKickout(@RequestBody List<Long> adminIds) {
        try {
            Long currentAdminId = StpUtil.getLoginIdAsLong();

            Map<String, Object> result = new HashMap<>();
            List<String> successList = new ArrayList<>();
            List<String> failedList = new ArrayList<>();

            for (Long adminId : adminIds) {
                try {
                    // 不能强制下线自己
                    if (currentAdminId.equals(adminId)) {
                        failedList.add("管理员ID " + adminId + ": 不能强制下线自己");
                        continue;
                    }

                    // 检查用户是否存在
                    Admin admin = adminService.getById(adminId);
                    if (admin == null) {
                        failedList.add("管理员ID " + adminId + ": 用户不存在");
                        continue;
                    }

                    // 检查是否在线
                    if (!StpUtil.isLogin(adminId)) {
                        failedList.add("管理员ID " + adminId + " (" + admin.getUsername() + "): 用户当前不在线");
                        continue;
                    }

                    // 执行强制下线
                    StpUtil.kickout(adminId);
                    successList.add("管理员ID " + adminId + " (" + admin.getUsername() + "): 强制下线成功");

                } catch (Exception e) {
                    failedList.add("管理员ID " + adminId + ": " + e.getMessage());
                }
            }

            result.put("successCount", successList.size());
            result.put("failedCount", failedList.size());
            result.put("successList", successList);
            result.put("failedList", failedList);

            log.info("批量强制下线用户完成，成功: {}, 失败: {}", successList.size(), failedList.size());
            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("批量强制下线用户失败", e);
            return CommonResult.failed("批量强制下线失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户在线状态
     */
    @Operation(summary = "检查用户在线状态")
    @GetMapping("/online/status/{adminId}")
    @SaCheckLogin
    @SaCheckPermission("admin:auth:online")
    @DataPermission(type = DataPermission.PermissionType.AUTO, description = "检查用户在线状态数据权限控制")
    public CommonResult<Map<String, Object>> checkOnlineStatus(@PathVariable Long adminId) {
        try {
            Admin admin = adminService.getById(adminId);
            if (admin == null) {
                return CommonResult.failed("用户不存在");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("adminId", adminId);
            result.put("username", admin.getUsername());
            result.put("realName", admin.getRealName());
            result.put("isOnline", StpUtil.isLogin(adminId));

            if (StpUtil.isLogin(adminId)) {
                // 获取在线详细信息
                List<String> tokenList = StpUtil.getTokenValueListByLoginId(adminId);
                result.put("tokenCount", tokenList.size());
                result.put("tokens", tokenList);

                // 获取最后活跃时间等信息
                if (!tokenList.isEmpty()) {
                    try {
                        result.put("loginDevice", "web"); // 默认设备类型
                        result.put("tokenTimeout", StpUtil.getTokenTimeout());
                        result.put("sessionTimeout", StpUtil.getSessionTimeout());
                        result.put("tokenActiveTimeout", StpUtil.getTokenActiveTimeout());
                    } catch (Exception e) {
                        log.debug("获取token信息失败: {}", e.getMessage());
                    }
                }
            } else {
                result.put("tokenCount", 0);
                result.put("tokens", new ArrayList<>());
            }

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("检查用户在线状态失败，用户ID: {}", adminId, e);
            return CommonResult.failed("检查在线状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取在线用户统计信息
     */
    @Operation(summary = "获取在线用户统计信息")
    @GetMapping("/online/statistics")
    @SaCheckLogin
    @SaCheckPermission("admin:auth:online")
    @DataPermission(type = DataPermission.PermissionType.AUTO, description = "获取在线用户统计信息数据权限控制")
    public CommonResult<Map<String, Object>> getOnlineStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // 获取所有在线token
            List<String> allTokens = StpUtil.searchTokenValue("", 0, -1, false);
            statistics.put("totalTokens", allTokens.size());

            // 统计不同类型的管理员在线数量
            Map<String, Integer> adminTypeCount = new HashMap<>();
            adminTypeCount.put("system", 0);
            adminTypeCount.put("entity", 0);
            adminTypeCount.put("partner", 0);
            adminTypeCount.put("shop", 0);

            int validUserCount = 0;
            for (String token : allTokens) {
                try {
                    Object loginId = StpUtil.getLoginIdByToken(token);
                    if (loginId != null) {
                        Admin admin = adminService.getById(Long.valueOf(loginId.toString()));
                        if (admin != null) {
                            validUserCount++;
                            String adminType = admin.getAdminType();
                            if (adminType != null && adminTypeCount.containsKey(adminType)) {
                                adminTypeCount.put(adminType, adminTypeCount.get(adminType) + 1);
                            }
                        }
                    }
                } catch (Exception e) {
                    // 忽略无效token
                }
            }

            statistics.put("onlineUserCount", validUserCount);
            statistics.put("adminTypeStatistics", adminTypeCount);
            statistics.put("timestamp", System.currentTimeMillis());

            return CommonResult.success(statistics);

        } catch (Exception e) {
            log.error("获取在线用户统计信息失败", e);
            return CommonResult.failed("获取统计信息失败: " + e.getMessage());
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 转换Admin为VO
     */
    private AdminVO convertToVO(Admin admin) {
        AdminVO vo = new AdminVO();
        vo.setId(admin.getId());
        vo.setUsername(admin.getUsername());
        vo.setRealName(admin.getRealName());
        vo.setAvatar(admin.getAvatar());
        vo.setAdminType(admin.getAdminType());
        vo.setMobile(admin.getMobile());
        vo.setEmail(admin.getEmail());
        vo.setStatus(admin.getStatus());
        vo.setLoginTime(admin.getLoginTime());
        vo.setLoginIp(admin.getLoginIp());
        vo.setEntityId(admin.getEntityId());
        vo.setPartnerId(admin.getPartnerId());
        vo.setShopId(admin.getShopId());
        vo.setCreateTime(admin.getCreateTime());
        vo.setUpdateTime(admin.getUpdateTime());
        // vo.setRemark(admin.getRemark()); // Admin实体类中没有remark字段
        return vo;
    }
}
